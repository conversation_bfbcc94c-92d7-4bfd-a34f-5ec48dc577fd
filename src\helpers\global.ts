
import { Order, OrderProduct } from "../navigation/type";

export function capitalizeString(str: string): string {
    if (str.length === 0) {
        return "";
    }
    return str.charAt(0).toUpperCase() + str.slice(1);
}

export const getCollectionStatusFromProducts = (allProducts: OrderProduct[]): 'created' | 'processing' | 'completed' => {
    const total = allProducts.length;
    const gatheredCount = allProducts.filter(p => p.status === 'gathered').length;

    if (gatheredCount === 0) return 'created';
    return 'processing';
};

