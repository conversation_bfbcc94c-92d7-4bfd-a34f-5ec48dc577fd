import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, ScrollView, TextInput, Alert, KeyboardAvoidingView, Platform, Dimensions, LayoutChangeEvent, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useRoute, useNavigation, RouteProp, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign, Feather, FontAwesome6, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import GlobalLayout from '../../components/GlobalLayout';
import ThemedText from '../../components/ThemedText';
import OrderDropdown from '../../components/OrderDropdown';
import ImagePicker from '../../components/ImagePicker';
import useTheme from '../../theme/useTheme';
import { AppDispatch, RootState } from '../../app/store';
import { addReturn, getReasons, updateReturn } from './returnsSlice';
import { fetchOrders, setFilter } from '../orders/orderSlice';
import { ReturnsStackParamList, Order, ReturnReason, DashboardFilter } from '../../navigation/type';
import DropDownPicker from 'react-native-dropdown-picker';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { useAnimatedStyle, useSharedValue, withRepeat, withSequence, withTiming } from 'react-native-reanimated';
import { RefreshControl, Swipeable } from 'react-native-gesture-handler';
import { Camera, CameraView } from 'expo-camera';
import { useAudioPlayer } from 'expo-audio';
import { useToast } from '../toast/ToastContext';
import SimpleDropdown from '../../components/SimpleDropdown';
import { Modalize } from 'react-native-modalize';
import dayjs from 'dayjs';
import ThemedTextInput from '../../components/ThemedTextInput';
import { TouchableOpacity as guesterTouchableOpacity } from 'react-native-gesture-handler';
import { TouchableOpacity as reactTouchableOpacity } from 'react-native';
import RenderEmptyState from '../../components/RenderEmptyState';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import ThemedSearchTextInput from '../../components/ThemedSearchTextInput';
const TouchableOpacity = Platform.OS === 'ios' ? reactTouchableOpacity : guesterTouchableOpacity;

const SuccessAudioSource = require('../../../assets/sounds/success.mp3');
const ErrorAudioSource = require('../../../assets/sounds/error.mp3');

type AddEditReturnScreenRouteProp = RouteProp<ReturnsStackParamList, 'AddReturn' | 'EditReturn'>;
type AddEditReturnScreenNavigationProp = NativeStackNavigationProp<ReturnsStackParamList>;


function SwipeableBarCodeRow({ onSwipe, theme, modalref, orders, selectedOrders, setSelectedOrders, loading, dispatch, selectedWarehouse }: { onSwipe: () => void; theme: any, modalref: any, orders: Order[], selectedOrders: Order[], setSelectedOrders: (orders: Order[]) => void, loading: boolean, dispatch: AppDispatch, selectedWarehouse: any }) {
    const swipeRef = useRef<Swipeable>(null);

    const screenWidth = Dimensions.get('window').width;
    const [cardHeight, setCardHeight] = useState<number | undefined>(undefined);

    const handleLayout = (e: LayoutChangeEvent) => {
        setCardHeight(e.nativeEvent.layout.height);
    };
    const swipeAnim = useSharedValue(0);

    useEffect(() => {
        swipeAnim.value = withRepeat(
            withSequence(
                withTiming(-10, { duration: 500 }),
                withTiming(0, { duration: 500 })
            ),
            -1, // infinite
            true // reverse
        );
    }, []);
    const animatedIconStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: swipeAnim.value }],
        };
    });

    const renderRightActions = (progress: any, dragX: any) => {


        return (
            <LinearGradient
                colors={['transparent', theme.success]}
                start={{ x: 0, y: 0.5 }}
                end={{ x: 1, y: 0.5 }}
                style={{
                    width: screenWidth,
                    justifyContent: 'center',
                    alignItems: 'flex-end',
                    paddingRight: 20,

                    // REMOVE marginVertical
                    borderTopRightRadius: 8,
                    borderBottomRightRadius: 8,
                }}
            >
                <MaterialCommunityIcons name="barcode-scan" size={24} color="white" />
            </LinearGradient>
        );
    };

    return (
        <Swipeable
            containerStyle={{ width: '100%' }}
            ref={swipeRef}
            renderRightActions={(progress, dragX) => renderRightActions(progress, dragX)}
            onSwipeableOpen={() => { onSwipe(); swipeRef.current?.close(); }}
        >


            <TouchableOpacity
                style={[
                    {
                        backgroundColor: theme.inputBackground,
                        borderColor: selectedOrders.length > 0 ? theme.primary : theme.text + '30',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingHorizontal: 16,
                        paddingVertical: 12,
                        borderRadius: 8,
                        borderWidth: 1,
                        minHeight: 56,
                    },
                ]}

                onPress={() => modalref.current?.open()}


            >
                <View style={{
                    flex: 1,
                    marginRight: 8,
                }}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', gap: 12 }}>
                        <ThemedText
                            numberOfLines={1}
                            ellipsizeMode="tail" variant="subtitle" style={{
                                color: selectedOrders.length > 0 ? theme.primary : theme.text + '60', fontSize: 16,
                                fontWeight: '600',
                                marginBottom: 4,

                            }}>
                            {selectedOrders.length > 0 ? selectedOrders.map((order) => order?.orderNum).join(', ') : 'Select an order'}
                        </ThemedText>
                        {/* <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                            <MaterialCommunityIcons
                                name="package-variant-closed"
                                size={24}
                                color={theme.primary}
                            />
                            <ThemedText variant="body" numberOfLines={1} style={{ color: theme.primary }}>
                                {selectedOrders[0]?.orderNum}
                            </ThemedText>
                        </View>
                        <View style={{ flex: 1, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                            <ThemedText variant="subtitle" style={{
                                color: theme.primary, fontSize: 16,
                                fontWeight: '600',
                                marginBottom: 4,
                            }}>
                                Order {selectedOrders[0]?.orderNum}
                            </ThemedText>

                        </View> */}
                    </View>

                </View>

                <AntDesign name='down' size={16} color={selectedOrders.length > 0 ? theme.primary : theme.text + '60'} />
            </TouchableOpacity>

        </Swipeable>
    );
}

export default function AddEditReturnScreen() {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<AddEditReturnScreenNavigationProp>();
    const route = useRoute<AddEditReturnScreenRouteProp>();

    const { orders, filter, statusKeys, loading: ordersLoading } = useSelector((state: RootState) => state.orders);
    const { returns, reasons, loading } = useSelector((state: RootState) => state.returns);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const SuccessPlayer = useAudioPlayer(SuccessAudioSource);
    const ErrorPlayer = useAudioPlayer(ErrorAudioSource);

    const isEditing = route.name === 'EditReturn';
    const returnId = isEditing ? (route.params as any)?.returnId : undefined;
    const existingReturn = isEditing ? returns.find(r => r.id === returnId) : undefined;


    const [selectedOrders, setSelectedOrders] = useState<Order[]>([]);
    const [selectedReason, setSelectedReason] = useState<string | null>(null);
    const [returnReason, setReturnReason] = useState('');
    const [images, setImages] = useState<string[]>([]);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});

    const [hasPermission, setHasPermission] = useState<boolean | null>(null);
    const [scanned, setScanned] = useState(false);
    const [isScanning, setIsScanning] = useState(false);
    const [scanSuccess, setScanSuccess] = useState(false);
    const [scanError, setScanError] = useState<string | null>(null);
    const [torchOn, setTorchOn] = useState(false);
    const modalRef = useRef<Modalize>(null);
    const [search, setSearch] = useState('');
    const debounceRef = useRef<NodeJS.Timeout | null>(null);



    const { showToast } = useToast();

    useEffect(() => {
        if (selectedWarehouse && selectedWarehouse.id) {
            dispatch(setFilter({ ...filter, ...{ keyword: '', status: statusKeys[2] } }));
        }
    }, [])


    useEffect(() => {
        // skip dispatch if the keyword is already the same
        if ((filter.keyword ?? '') === (search ?? '')) return;

        if (debounceRef.current) clearTimeout(debounceRef.current);

        console.log('filter', filter);


        debounceRef.current = setTimeout(() => {
            // normalize to empty string when cleared
            const keyword = (search || '').trim();
            dispatch(setFilter({ ...filter, ...{ keyword: keyword, status: statusKeys[2] } }));
        }, 400); // tweak delay as you like

        return () => {
            if (debounceRef.current) clearTimeout(debounceRef.current);
        };
        // only depend on `search` so tab/date changes don't re-fire this
    }, [search]);

    const handleSearch = () => {
        const keyword = (search || '').trim();
        if ((filter.keyword ?? '') !== keyword) {
            dispatch(setFilter({ ...filter, ...{ keyword: keyword, status: statusKeys[2] } }));
        }
    };

    useEffect(() => {
        if (selectedWarehouse && selectedWarehouse.id) {
            dispatch(fetchOrders({ collectionId: null, warehouseId: selectedWarehouse.id }))
        }

    }, [filter, selectedWarehouse?.id])

    useEffect(() => {
        dispatch(getReasons());
    }, [dispatch]);

    const toggleTorch = () => {
        setTorchOn((prev) => !prev);
    };

    // Audio feedback functions
    const playSuccessSound = async () => {
        try {
            SuccessPlayer.seekTo(0);
            SuccessPlayer.play();
        } catch (err) {
            console.warn('Failed to play success sound', err);
        }
    };

    const playErrorSound = async () => {
        try {
            ErrorPlayer.seekTo(0);
            ErrorPlayer.play();
        } catch (err) {
            console.warn('Failed to play error sound', err);
        }
    };
    useEffect(() => {
        // Fetch orders if not already loaded
        if (orders.length === 0) {
            // if (selectedWarehouse) {
            //     dispatch(AllOrders(selectedWarehouse.id));
            // } else {
            //     showToast('Please select a warehouse first', 'error');
            // }
        }

        // If editing, populate form with existing data
        if (isEditing && existingReturn) {
            if (existingReturn.orders) setSelectedOrders(existingReturn.orders as Order[]);
            setSelectedReason(existingReturn.returnReason ?? null);
            setReturnReason(existingReturn.returnComment ?? '');
            setImages(existingReturn.images ?? []);
        }
    }, [isEditing, existingReturn, orders]);

    const validateForm = (): boolean => {

        if (!selectedOrders || selectedOrders.length === 0) {
            showToast('Please select an order', 'error');
            return false;
        }
        if (!selectedReason) {
            showToast('Please select a reason', 'error');
            return false;
        }
        if (hasComponent('comment') && !returnReason.trim()) {
            showToast('Please enter a return reason', 'error');
            return false;
        }
        if (hasComponent('image') && images.length === 0) {
            showToast('Please add at least one image', 'error');
            return false;
        }

        return true;
    };
    const OpenScanner = () => {
        if (hasPermission === true) {
            setIsScanning(true);
            setScanned(false);
        } else {
            Camera.requestCameraPermissionsAsync().then(({ status }) => {
                setHasPermission(status === 'granted');
                if (status === 'granted') {
                    setIsScanning(true);
                    setScanned(false);
                }
            });
        }

    };
    const handleSave = async () => {

        if (!validateForm()) {
            return;
        }

        try {



            const formData = new FormData();

            formData.append('orderIds', selectedOrders.map((order) => order.id).join(';'));
            formData.append('returnReason', selectedReason!);

            if (hasComponent('comment')) { formData.append('returnComment', returnReason.trim()); }

            if (hasComponent('image')) {
                images.forEach((uri, index) => {
                    const name = uri.split('/').pop() || `image_${index}.jpg`;
                    const ext = name.split('.').pop()?.toLowerCase();
                    const type =
                        ext === 'png' ? 'image/png' :
                            ext === 'webp' ? 'image/webp' :
                                ext === 'gif' ? 'image/gif' :
                                    'image/jpeg';

                    formData.append('images[]', {
                        uri,
                        name,
                        type,
                    } as any);
                });
            }



            if (isEditing && returnId) {
                const res = await dispatch(updateReturn({ id: returnId, formData }))


                if (updateReturn.fulfilled.match(res)) {
                    showToast('Return updated successfully', 'success');
                    navigation.goBack();
                } else {
                    //read the rejected error
                    showToast(res.payload as string, 'error');
                }
            } else {
                const res = await dispatch(addReturn(formData))
                if (addReturn.fulfilled.match(res)) {
                    showToast('Return added successfully', 'success');
                    navigation.goBack();
                } else {
                    //read the rejected error
                    showToast(res.payload as string, 'error');
                }
            }


        } catch (error) {
            showToast('Failed to save return. Please try again.', 'error');
        }
    };

    const handleCancel = () => {
        if (selectedOrders.length > 0 || returnReason.trim() || images.length > 0) {
            Alert.alert(
                'Discard Changes',
                'Are you sure you want to discard your changes?',
                [
                    { text: 'Keep Editing', style: 'cancel' },
                    { text: 'Discard', style: 'destructive', onPress: () => navigation.goBack() },
                ]
            );
        } else {
            navigation.goBack();
        }
    };





    const handleBarcodeScanned = async (scanningResult: { type: string; data: string }) => {
        setScanned(true);
        setIsScanning(false);

        //get the order from orders
        const scannedOrder = orders.find(order => order.orderCode === scanningResult.data);
        if (scannedOrder) {
            //add the scannedOrder to the selectedOrders\
            setSelectedOrders([...selectedOrders, scannedOrder]);
            playSuccessSound();
            setScanError(null);
            setScanSuccess(true);
        } else {
            setSelectedOrders(selectedOrders);
            setScanError(`Order scanned not found`);
            playErrorSound();
            return;
        }

    };

    const animatedScannerHeight = useSharedValue(0);
    const overlayOpacity = useSharedValue(0);
    const animatedScannerStyle = useAnimatedStyle(() => {
        return {
            height: animatedScannerHeight.value,
            opacity: animatedScannerHeight.value === 0 ? 0 : 1,
        };
    });
    const overlayAnimatedStyle = useAnimatedStyle(() => {
        return {
            opacity: overlayOpacity.value,
        };
    });

    const scanLineTranslateY = useSharedValue(0);


    const scanLineStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateY: scanLineTranslateY.value }],
        };
    });


    useEffect(() => {
        if (isScanning) {
            // Step 1: Expand scanner
            animatedScannerHeight.value = withTiming(400, { duration: 300 }, (finished) => {
                if (finished) {
                    // Step 2: Fade in overlay
                    overlayOpacity.value = withTiming(1, { duration: 300 });
                }
            });

            // Start scan line animation
            scanLineTranslateY.value = 0;
            scanLineTranslateY.value = withRepeat(
                withTiming(240, { duration: 1000 }),
                -1,
                true
            );

        } else {
            // Step 1: Fade out overlay
            overlayOpacity.value = withTiming(0, { duration: 200 }, (finished) => {
                if (finished) {
                    // Step 2: Collapse scanner
                    animatedScannerHeight.value = withTiming(0, { duration: 300 });
                }
            });
        }
    }, [isScanning]);

    const hasComponent = (component: string): boolean => {
        if (!selectedReason || !reasons || reasons.length === 0) return false;

        const matchedReason = reasons.find(r => r.Label === selectedReason);
        if (!matchedReason) return false;

        switch (component) {
            case 'comment':
                return !!matchedReason.requireComment;
            case 'image':
                return !!matchedReason.requireImage;
            default:
                return false;
        }
    };



    return (
        <GlobalLayout>

            <View style={styles.container}>
                <KeyboardAvoidingView
                    style={styles.keyboardAvoid}
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
                >
                    {/* Header */}
                    <View style={styles.header}>
                        <View style={styles.headerLeft}>
                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={handleCancel}
                                disabled={loading}
                            >
                                <AntDesign name="arrowleft" size={24} color={theme.text} />
                            </TouchableOpacity>
                            <ThemedText variant="subtitle" style={styles.headerTitle}>
                                {isEditing ? 'Edit Return' : 'Add Return'}
                            </ThemedText>
                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={handleSave}
                                disabled={loading}
                            >
                                <ThemedText variant="body" style={{ color: theme.primary }} >
                                    {isEditing ? 'Update' : 'Create'}
                                </ThemedText>
                                {loading && <ActivityIndicator size="small" color={theme.primary} />}
                            </TouchableOpacity>
                        </View>
                    </View>

                    <ScrollView
                        style={styles.scrollView}
                        contentContainerStyle={styles.scrollContent}
                        showsVerticalScrollIndicator={false}
                    >

                        {/* Order Selection */}
                        <View style={styles.section}>
                            <View style={{ flexDirection: 'column', display: 'flex', width: '100%', marginBottom: 8, justifyContent: 'flex-start', alignItems: 'flex-start', gap: 2 }} >
                                <ThemedText variant="subtitle" style={[styles.label, { marginBottom: 0 }]}>
                                    Order *
                                </ThemedText>
                                <View style={{ flexDirection: 'row', display: 'flex', width: '100%', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12 }} >
                                    {!isEditing && (
                                        <>
                                            <ThemedText variant="body" style={[{ color: theme.text + '60' }]}>
                                                Swipe to scan the barcode
                                            </ThemedText>
                                            <MaterialCommunityIcons name="barcode-scan" size={18} color={theme.text + '60'} />
                                        </>)}</View>
                            </View>


                            <View style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 16 }}>

                                {
                                    isEditing ?
                                        <View>

                                            <View style={{ flexDirection: 'row', display: 'flex', width: '100%', justifyContent: 'flex-start', alignItems: 'center', gap: 8 }}>
                                                <ThemedText variant="body" >
                                                    Order
                                                </ThemedText>
                                                <ThemedText variant="body" >
                                                    {selectedOrders.map((order) => order?.orderNum).join(', ')}
                                                </ThemedText>
                                                <ThemedText variant="body" style={{ color: theme.error }} >
                                                    (Cannot be changed)
                                                </ThemedText>

                                            </View>
                                        </View>
                                        :
                                        <SwipeableBarCodeRow
                                            modalref={modalRef}
                                            onSwipe={OpenScanner}
                                            theme={theme}
                                            orders={orders}
                                            selectedOrders={selectedOrders}
                                            setSelectedOrders={setSelectedOrders}
                                            loading={loading}
                                            dispatch={dispatch}
                                            selectedWarehouse={selectedWarehouse}
                                        />
                                }

                                <Animated.View style={[styles.scannerContainer, animatedScannerStyle]}>
                                    {isScanning || animatedScannerHeight.value > 0 ?
                                        <>
                                            <CameraView
                                                enableTorch={torchOn}
                                                style={styles.scanner}
                                                facing="back"
                                                barcodeScannerSettings={{
                                                    barcodeTypes: ["qr", "pdf417", "code128", "code39", "code93", "codabar", "ean13", "ean8", "upc_e", "upc_a"],
                                                }}
                                                onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
                                            />

                                            <Animated.View style={[styles.scanOverlay, overlayAnimatedStyle]}>
                                                <TouchableOpacity
                                                    style={styles.closeButton}
                                                    onPress={() => setIsScanning(false)}
                                                >
                                                    <AntDesign name="close" size={24} color="white" />
                                                    <ThemedText variant="body" style={{ color: 'white' }}>
                                                        Close
                                                    </ThemedText>

                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    style={styles.torchButton}
                                                    onPress={toggleTorch}
                                                >
                                                    <Ionicons name={torchOn ? 'flash' : 'flash-off'} size={24} color="white" />
                                                </TouchableOpacity>
                                                <View style={styles.scanFrame} >

                                                    <Animated.View style={[styles.scanLine, scanLineStyle]}>
                                                        <LinearGradient
                                                            colors={['transparent', 'rgba(255,0,0,0.7)', 'transparent']}
                                                            style={{ flex: 1 }}
                                                            start={[0, 0]}
                                                            end={[1, 0]}
                                                        />
                                                    </Animated.View>

                                                </View>
                                                {/* error */}

                                            </Animated.View>
                                        </>
                                        : null

                                    }</Animated.View>
                            </View>
                            {scanError && (
                                <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', gap: 8, width: '100%', marginTop: 8 }}>

                                    <ThemedText variant="body" style={[{ color: '#d32f2f' }]}>
                                        {scanError}
                                    </ThemedText>

                                </View>
                            )}
                            {errors.order && (
                                <ThemedText style={[styles.errorText, { color: theme.error }]}>
                                    {errors.order}
                                </ThemedText>
                            )}
                            <View style={{ flexDirection: 'column', display: 'flex', width: '100%', marginBottom: 8, justifyContent: 'flex-start', alignItems: 'flex-start', gap: 2 }} >
                                <ThemedText variant="subtitle" style={[styles.label, { marginBottom: 0 }]}>
                                    Reason *
                                </ThemedText>
                                <SimpleDropdown
                                    data={reasons.map(r => r.Label)}
                                    selected={selectedReason}
                                    onSelect={setSelectedReason}
                                    placeholder="Select a reason"
                                    disabled={loading}
                                    title="Select a reason"
                                />
                            </View>

                        </View>

                        {hasComponent('comment') &&
                            <View style={styles.section}>
                                <ThemedText variant="subtitle" style={styles.label}>
                                    Comment
                                </ThemedText>
                                <TextInput
                                    style={[
                                        styles.textArea,
                                        {
                                            backgroundColor: theme.inputBackground,
                                            color: theme.text,
                                            borderColor: errors.reason ? theme.error : theme.text + '30'
                                        }
                                    ]}
                                    value={returnReason}
                                    onChangeText={setReturnReason}
                                    placeholder="Describe the reason for return..."
                                    placeholderTextColor={theme.text + '60'}
                                    multiline
                                    numberOfLines={4}
                                    textAlignVertical="top"
                                    editable={!loading}
                                />
                                {errors.reason && (
                                    <ThemedText style={[styles.errorText, { color: theme.error }]}>
                                        {errors.reason}
                                    </ThemedText>
                                )}
                            </View>}
                        {hasComponent('image') &&
                            <View style={styles.section}>
                                <ImagePicker
                                    images={images}
                                    onImagesChange={setImages}
                                    maxImages={5}
                                />
                                {errors.images && (
                                    <ThemedText style={[styles.errorText, { color: theme.error }]}>
                                        {errors.images}
                                    </ThemedText>
                                )}
                            </View>}

                    </ScrollView>


                </KeyboardAvoidingView>
            </View>
            <Modalize
                ref={modalRef}
                modalHeight={Dimensions.get('window').height * 0.6}
                modalStyle={[styles.modal, { backgroundColor: theme.cardBackground }]}
                handleStyle={{ backgroundColor: theme.primary }}
                withHandle
                HeaderComponent={
                    <View style={{ paddingHorizontal: 10, height: 80, width: '100%', gap: 4, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                        <ThemedSearchTextInput
                            placeholder="Search order"
                            value={search}
                            onChangeText={setSearch}
                            autoCapitalize="none"
                            returnKeyType="go"
                            onSubmitEditing={handleSearch}
                            style={{ flex: 1 }}
                            endComponent={
                                ordersLoading ? <ActivityIndicator size="small" color={theme.primary} style={{ marginRight: 8 }} /> : null
                            }
                        />
                        <TouchableOpacity style={{ paddingHorizontal: 5, paddingVertical: 5, borderRadius: 10, }} onPress={() => {
                            if (selectedOrders.length > 0 && selectedOrders.length === orders.length) {
                                setSelectedOrders([]);
                            } else {
                                setSelectedOrders(orders);
                            }
                        }}>
                            <Feather name={selectedOrders.length > 0 && selectedOrders.length === orders.length ? "check-square" : selectedOrders.length > 0 ? "minus-square" : "square"} size={24} color={theme.text} />
                        </TouchableOpacity>

                    </View>
                }
            >
                {/* just the list scrolls */}
                <ScrollView >
                    {orders.length === 0 ? <RenderEmptyState state="Order" subtitle="No orders found" /> :
                        (Array.isArray(orders) ? orders : []).map(order => {
                            const isSelected = selectedOrders.find(o => o.id === order.id);
                            return (
                                <TouchableOpacity
                                    onPress={() => {
                                        if (isSelected) {
                                            setSelectedOrders(selectedOrders.filter(o => o.id !== order.id));
                                        } else {
                                            setSelectedOrders([...selectedOrders, order]);
                                        }
                                    }}
                                    key={order.id}
                                    style={{
                                        flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 10, margin: 5, borderRadius: 10,
                                        borderBottomWidth: 1, borderBottomColor: theme.text + '20', borderLeftWidth: isSelected ? 5 : 0, borderLeftColor: isSelected ? theme.primary : theme.text + '20', backgroundColor: isSelected ? theme.primary + '10' : 'transparent'
                                    }}>
                                    <View style={{ flex: 1 }}>
                                        <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', gap: 4 }}>
                                            <ThemedText variant="body" style={{ color: theme.text + '90', fontWeight: 'bold' }}>
                                                {order.orderNum}
                                            </ThemedText>
                                            <ThemedText variant="body" style={{ color: theme.text + '60' }}>
                                                {order.orderCode}
                                            </ThemedText>
                                        </View>
                                        <ThemedText variant="body" style={{ color: theme.text + '60' }}>
                                            Shipped At {dayjs(order.shippedAt).format('DD/MM/YYYY')}
                                        </ThemedText>
                                    </View>

                                    <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
                                        <Feather name={isSelected ? "check-square" : "square"} size={24} color={theme.text} />
                                    </View>

                                </TouchableOpacity>
                            )
                        })}
                </ScrollView>
            </Modalize>


        </GlobalLayout>
    );
}

const styles = StyleSheet.create({
    modal: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    modalContent: {
        padding: 20,
    },
    modalTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    headerSection: {
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        paddingVertical: 16,
    },
    headerModal: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
    },
    container: {
        flex: 1,
    },
    keyboardAvoid: {
        flex: 1,
    },
    header: {
        paddingLeft: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    headerLeft: {
        flexDirection: 'row',
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    backButton: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 4,
        marginRight: 16,
        padding: 4,
    },
    headerTitle: {
        flex: 1,
    },
    scrollView: {
        flex: 1,
    },
    scrollContent: {
        paddingHorizontal: 20,
        paddingVertical: 16,
    },
    section: {
        marginBottom: 24,
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
    },
    label: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
    },
    textArea: {
        borderWidth: 1,
        borderRadius: 8,
        padding: 16,
        fontSize: 16,
        minHeight: 100,
        width: '100%',
    },
    errorText: {
        fontSize: 14,
        marginTop: 4,
        width: '100%',
    },
    footer: {
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
    scannerContainer: {
        overflow: 'hidden',
        flex: 1,
        borderRadius: 16,
        width: '100%',
        position: 'relative',
    },
    scanner: {
        flex: 1,
    },
    scanContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanFrame: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: 250,
        height: 250,
        borderWidth: 2,
        borderColor: '#007AFF',
        borderRadius: 12,
        backgroundColor: 'transparent',
    },
    scanLine: {
        position: 'absolute',
        width: '90%',
        zIndex: 1,
        height: 4,
        borderRadius: 2,
        overflow: 'hidden',
        top: 0,
        alignSelf: 'center',
    },
    torchButton: {
        position: 'absolute',
        top: 16,
        right: 16,
        padding: 8,
        borderRadius: 8,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    closeButton: {
        position: 'absolute',
        top: 16,
        left: 16,
        padding: 8,
        borderRadius: 8,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        display: 'flex',
        justifyContent: 'center',
    },
});
