import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, FlatList, Alert, Dimensions, TouchableOpacity as ReactTouchableOpacity, Platform, Image, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign, FontAwesome } from '@expo/vector-icons';
import { Modalize } from 'react-native-modalize';
import ThemedText from '../../components/ThemedText';
import GlobalLayout from '../../components/GlobalLayout';
import ThreeDotsMenu from '../../components/ThreeDotsMenu';
import useTheme from '../../theme/useTheme';
import { AppDispatch, RootState } from '../../app/store';
import { fetchReturns, deleteReturn } from './returnsSlice';
import { Order, Return, ReturnsStackParamList } from '../../navigation/type';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import dayjs from 'dayjs';
import { TouchableOpacity as guesterTouchableOpacity } from 'react-native-gesture-handler';
import { TouchableOpacity as reactTouchableOpacity } from 'react-native';
import RenderEmptyState from '../../components/RenderEmptyState';
import ImageView from "react-native-image-viewing";
import { useToast } from '../toast/ToastContext';
import { AllOrders, fetchOrders } from '../orders/orderSlice';

const TouchableOpacity = Platform.OS === 'ios' ? reactTouchableOpacity : guesterTouchableOpacity;
type ReturnsScreenNavigationProp = NativeStackNavigationProp<ReturnsStackParamList, 'ReturnsList'>;

type ReturnListItemProps = {
    return: Return;
    orderNum: number;
    onPress: () => void;
    onEdit: () => void;
    onDelete: () => void;
};

function ReturnListItem({ return: returnItem, orderNum, onPress, onEdit, onDelete }: ReturnListItemProps) {
    const theme = useTheme();


    return (
        <View
            style={[styles.listItem, { backgroundColor: theme.cardBackground }]}

        >
            <View style={styles.itemContent}>
                <ReactTouchableOpacity style={[styles.itemHeader, { borderBottomColor: theme.text + '30' }]} onPress={onPress}>
                    <ThemedText variant="title" style={styles.refText}>
                        {returnItem.ref}
                    </ThemedText>
                    {/* arrow down icon  */}
                    <AntDesign name="down" size={20} color={theme.text} />
                </ReactTouchableOpacity>

                <View style={styles.itemInfo}>
                    <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', flex: 1, gap: 8 }}>
                        <FontAwesome name="cube" size={20} color={theme.text} />
                        <ThemedText variant="body" style={[styles.orderText, { color: theme.text, fontWeight: 'bold' }]}>
                            {orderNum}
                        </ThemedText>
                    </View>
                    <ThemedText variant='body' style={[styles.orderText, { color: theme.text + '80' }]}>{dayjs(returnItem.createdAt).format('DD/MM/YYYY')}</ThemedText>

                </View>
            </View>
        </View>
    );
}

export default function ReturnsScreen() {
    const { showToast } = useToast();

    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<ReturnsScreenNavigationProp>();
    const { returns, loading } = useSelector((state: RootState) => state.returns);
    const { orders } = useSelector((state: RootState) => state.orders);
    const modalRef = useRef<Modalize>(null);
    const [selectedReturn, setSelectedReturn] = useState<Return | null>(null);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const [isImageViewerVisible, setImageViewerVisible] = useState(false);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);


    useEffect(() => {
        if (selectedWarehouse) {
            dispatch(fetchReturns(selectedWarehouse.id));
            dispatch(AllOrders(selectedWarehouse.id));
        }
    }, [dispatch, selectedWarehouse]);

    const onRefresh = async () => {
        if (selectedWarehouse) {
            await dispatch(fetchReturns(selectedWarehouse.id));
            await dispatch(AllOrders(selectedWarehouse.id));
        }
    };



    const getOrderNumber = (selectedOrders: Order[]): string => {

        const ordersNum = orders.filter(o => selectedOrders?.map(order => order.id).includes(o.id))?.map(o => o.orderNum).join(', ');

        return ordersNum || 'undefined';
    };

    const handleReturnPress = (returnItem: Return) => {
        setSelectedReturn(returnItem);
        modalRef.current?.open();
    };

    const handleEdit = (returnItem: Return) => {
        navigation.navigate('EditReturn', { returnId: returnItem.id });
    };
    const [loadingDelete, setLoadingDelete] = useState(false)

    const Delete = async (id: number) => {
        try {
            setLoadingDelete(true);
            const res = await dispatch(deleteReturn(id));
            if (deleteReturn.fulfilled.match(res)) {
                showToast('Return deleted', 'success');
                modalRef.current?.close();
            }

        } catch (error) {
            console.log('Failed to delete return', error);
        }
        finally {
            setLoadingDelete(false);
        }
    };

    const handleDelete = (returnItem: Return) => {
        Alert.alert(
            'Delete Return',
            `Are you sure you want to delete return ${returnItem.ref}?`,
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => Delete(returnItem.id)
                },
            ]
        );
    };

    const handleAddReturn = () => {
        navigation.navigate('AddReturn');
    };

    const renderReturnItem = ({ item }: { item: Return }) => (
        <ReturnListItem
            return={item}
            orderNum={item.orders.length}
            onPress={() => handleReturnPress(item)}
            onEdit={() => handleEdit(item)}
            onDelete={() => handleDelete(item)}
        />
    );


    return (
        <GlobalLayout>

            <View style={styles.container}>
                <View style={styles.header}>
                    <ThemedText variant="title">Returns</ThemedText>
                    <ReactTouchableOpacity
                        style={[styles.addButton, { backgroundColor: theme.primary }]}
                        onPress={handleAddReturn}
                    >
                        <AntDesign name="plus" size={24} color="white" />
                        <ThemedText variant="body" style={{ color: 'white' }}>
                            New Return
                        </ThemedText>
                    </ReactTouchableOpacity>
                </View>

                <FlatList
                    data={[...returns].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())}
                    renderItem={renderReturnItem}
                    keyExtractor={(item) => item.id.toString()}
                    contentContainerStyle={[
                        styles.listContainer,
                        returns.length === 0 && styles.emptyListContainer
                    ]}
                    ListEmptyComponent={() => <RenderEmptyState state="Return" />}
                    showsVerticalScrollIndicator={false}
                    refreshing={loading}
                    onRefresh={onRefresh}
                />

                {/* Return Details Modal */}
                <Modalize

                    ref={modalRef}
                    modalHeight={Dimensions.get('window').height * 0.8}
                    modalStyle={[styles.modal, { backgroundColor: theme.cardBackground }]}
                    handleStyle={{ backgroundColor: theme.primary }}
                    withHandle
                //panGestureEnabled={false}
                >
                    {selectedReturn && (
                        <View style={styles.modalContent}>
                            {/* Header */}
                            <View style={styles.headerModal}>
                                <ThemedText variant="title" style={styles.modalTitle}>
                                    Return Details
                                </ThemedText>
                                {/* edit pencil */}
                                <TouchableOpacity
                                    style={styles.editButton}
                                    onPress={() => handleEdit(selectedReturn)}
                                >
                                    <AntDesign name="edit" size={24} color={theme.text} />
                                </TouchableOpacity>
                            </View>
                            <View style={{ flex: 1, width: '100%', minHeight: Dimensions.get('window').height * 0.5, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                                <View style={[styles.headerSection]}>
                                    <ThemedText variant="body" style={[styles.modalOrderText, { color: theme.text + '80' }]}>
                                        Return Ref : {selectedReturn.ref}
                                    </ThemedText>
                                    <ThemedText variant="body" style={[styles.modalOrderText, { color: theme.text + '80' }]}>
                                        Order : {getOrderNumber(selectedReturn.orders)}
                                    </ThemedText>
                                </View>

                                <View style={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '100%', gap: 8 }}>
                                    <ThemedText variant="subtitle" style={{ color: theme.text + '80', fontSize: 16, }}>
                                        Reason
                                    </ThemedText>
                                    <ThemedText variant="body" style={[styles.tag]}>
                                        {selectedReturn.returnReason}
                                    </ThemedText>
                                </View>


                                {selectedReturn.images.length > 0 &&
                                    <View style={styles.imagesSection}>
                                        <ThemedText variant="subtitle" style={styles.sectionTitle}>
                                            Images
                                        </ThemedText>
                                        <View style={styles.imagesList}>
                                            {selectedReturn.images.map((url, index) => (
                                                <TouchableOpacity
                                                    key={index}
                                                    onPress={() => {
                                                        setSelectedImageIndex(index);
                                                        setImageViewerVisible(true);
                                                    }}
                                                >
                                                    <Image source={{ uri: url }} style={styles.image} />
                                                </TouchableOpacity>
                                            ))}

                                        </View>
                                    </View>}

                                {selectedReturn.returnComment && <View style={styles.reasonSection}>
                                    <ThemedText variant="subtitle" style={styles.sectionTitle}>
                                        Comment
                                    </ThemedText>
                                    <ThemedText variant="body" style={[styles.reasonText, { borderLeftColor: theme.text + '80' }]}>
                                        {selectedReturn.returnReason}
                                    </ThemedText>
                                </View>
                                }
                            </View>


                            <View style={styles.deleteSection}>
                                <ThemedText variant="subtitle" style={[styles.deleteTitle, { color: theme.error }]}>
                                    Delete this return
                                </ThemedText>
                                <ThemedText variant="body" style={[styles.deleteWarning, { color: theme.error }]}>
                                    Once you delete a return, there is no going back. Please be certain.
                                </ThemedText>

                                <TouchableOpacity
                                    style={[styles.deleteButton, { borderColor: theme.error || '#ff4444' }]}
                                    onPress={() => handleDelete(selectedReturn)}
                                >
                                    <ThemedText style={[styles.deleteButtonText, { color: theme.error || '#ff4444' }]}>
                                        Delete this return
                                    </ThemedText>
                                    {loadingDelete && <ActivityIndicator size="small" color={theme.error || '#ff4444'} />}
                                </TouchableOpacity>
                            </View>

                        </View>
                    )}
                </Modalize>
                <ImageView

                    backgroundColor={theme.cardBackground}
                    images={selectedReturn?.images.map((uri) => ({ uri })) || []}
                    imageIndex={selectedImageIndex}
                    visible={isImageViewerVisible}
                    onRequestClose={() => setImageViewerVisible(false)}
                    swipeToCloseEnabled
                    doubleTapToZoomEnabled
                    presentationStyle="overFullScreen"
                />
            </View >
        </GlobalLayout >
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    tag: {
        borderRadius: 10,
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 16,
    },
    addButton: {
        display: 'flex',
        flexDirection: 'row',
        gap: 4,
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    listContainer: {
        paddingHorizontal: 20,
    },
    emptyListContainer: {
        flex: 1,
    },
    listItem: {
        borderRadius: 12,
        marginBottom: 12,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    itemContent: {
        display: 'flex',
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 10,
    },
    itemInfo: {
        flex: 1,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        paddingHorizontal: 10,
        paddingVertical: 10,
    },
    itemHeader: {
        width: '100%',
        paddingHorizontal: 10,
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 1,
        justifyContent: 'space-between',
        paddingVertical: 5,

    },
    refText: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 4,
    },
    orderText: {
        fontSize: 14,
    },
    modal: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    modalContent: {
        padding: 20,
        flex: 1,
        justifyContent: 'space-between',
    },
    modalTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    headerSection: {
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        paddingVertical: 16,
    },
    headerModal: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
    },
    editButton: {
        padding: 8,

    },
    modalOrderText: {
        fontSize: 16,
    },
    reasonSection: {
        paddingVertical: 16,
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
    sectionTitle: {
        fontSize: 18,
        paddingVertical: 16,
        fontWeight: '600',
    },
    reasonText: {
        fontSize: 16,
        lineHeight: 24,
        borderLeftWidth: 3,
        paddingLeft: 10,
    },
    imagesSection: {
        paddingVertical: 16,
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
    imagesList: {

        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
    },
    image: {
        width: 80,
        height: 80,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    deleteSection: {
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
        paddingTop: 16,
        alignSelf: 'flex-end',
        marginTop: 'auto'
    },
    deleteTitle: {
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 6,
    },
    deleteWarning: {
        fontSize: 14,
        marginBottom: 16,
        color: '#444',
    },
    deleteButton: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 4,
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderWidth: 1,
        borderRadius: 8,
        alignSelf: 'flex-start',
    },
    deleteButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },

});
