import React, { useEffect, useState, useCallback, useRef } from 'react';
import { View, ScrollView, RefreshControl, Alert, Dimensions, StyleSheet, Platform, Text, NativeSyntheticEvent, NativeScrollEvent, ActivityIndicator } from 'react-native';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AntDesign, Entypo, Feather, FontAwesome, FontAwesome5, FontAwesome6, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    interpolate,
    runOnJS
} from 'react-native-reanimated';
import * as Print from 'expo-print';
import { shareAsync } from 'expo-sharing';

import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import CollectionDetailsCard from '../../components/CollectionDetailsCard';
import { AppDispatch, RootState } from '../../app/store';
import { fetchOrders } from '../orders/orderSlice';
import { useDispatch, useSelector } from 'react-redux';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import { Order, OrderProduct, CollectionStackParamList, Collection } from '../../navigation/type';
import { fetchCollectionById, fetchCollectionProducts, fetchCollectionsByWarehouseId } from './CollectionsThunks';
import { replaceCollection } from './collectionSlice';
import { generateCollectionDetailsPDF, generateStyledCollectionDetailsPDF } from '../../utils/pdfGenerator';
import GlobalLayout from '../../components/GlobalLayout';
import { Modalize } from 'react-native-modalize';
import { TouchableOpacity as guesterTouchableOpacity } from 'react-native-gesture-handler';
import { TouchableOpacity as reactTouchableOpacity } from 'react-native';
import { BarcodeView } from 'rn-barcode-renderer';
import dayjs from 'dayjs';
import { useToast } from '../toast/ToastContext';
import { GetExcel } from '../../utils/excelHandler';
import OrderStatus from '../../components/OrderStatus';

const TouchableOpacity = Platform.OS === 'ios' ? reactTouchableOpacity : guesterTouchableOpacity;

type CollectionDetailsRouteProp = RouteProp<CollectionStackParamList, 'CollectionDetails'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'CollectionDetails'>;

export default function CollectionDetailsScreen() {
    const { params: { collectionId } } = useRoute<CollectionDetailsRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    const theme = useTheme();
    const { loading: ordersLoading, orders, statusKeys } = useSelector((state: RootState) => state.orders);
    const { loading: collectionLoading } = useSelector((state: RootState) => state.collections);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const [refreshing, setRefreshing] = useState(false);
    const [buttonsPosition, setButtonsPosition] = useState(0);
    const scrollViewRef = useRef<ScrollView>(null);
    const buttonsRef = useRef<View>(null);
    const [localCollection, setLocalCollection] = useState<Collection | null>(null);
    const scrollY = useSharedValue(0);
    const stickyHeaderOpacity = useSharedValue(0);
    const stickyHeaderHeight = useSharedValue(60);
    const stickyHeaderTranslateY = useSharedValue(-60);
    const tabsOpacity = useSharedValue(0);
    const tabsTranslateY = useSharedValue(-60);
    const [selectedTab, setSelectedTab] = useState(0);
    const { showToast } = useToast();
    const [PrintLoading, setPrintLoading] = useState(false)
    const [PrintCollectLoading, setPrintCollectLoadingLoading] = useState(false)
    const [ExcelLoading, setExcelLoading] = useState(false)


    const prevWarehouseId = useRef<number | null>(null);


    const allProducts = (orders || []).flatMap(order =>
        order.ordersProducts.map(product => ({ ...product, orderNum: order.orderNum }))
    );

    const groupedProducts: { [sku: string]: { name: string; totalQty: number; items: { product: OrderProduct; orderNum: string }[] } } = {};

    allProducts.forEach(({ sku, name, quantity, ...rest }) => {
        if (!groupedProducts[sku]) {
            groupedProducts[sku] = { name, totalQty: 0, items: [] };
        }
        groupedProducts[sku].totalQty += quantity;
        groupedProducts[sku].items.push({ product: { sku, name, quantity, ...rest }, orderNum: rest.orderNum });
    });

    const tabsDef = [
        { label: 'Orders', Count: orders.length },
        { label: 'Summary', Count: groupedProducts ? Object.keys(groupedProducts).length : 0 }
    ]
    const printCollect = async () => {
        setPrintCollectLoadingLoading(true)
        try {
            if (localCollection) {

                await generateStyledCollectionDetailsPDF(localCollection, orders);
            }
        } catch (err) {
            console.error('Print error:', err);
        }
        setPrintCollectLoadingLoading(false)
    }
    const printCollectionSummary = async () => {
        setPrintLoading(true)
        const res = await dispatch(fetchCollectionProducts(collectionId));
        if (fetchCollectionProducts.fulfilled.match(res)) {
            // cast as product 
            const allProducts = res.payload as OrderProduct[];
            const groupedProducts: { [sku: string]: { name: string; totalQty: number; items: { product: OrderProduct; orderNum: string }[] } } = {};

            allProducts.forEach(({ sku, name, quantity, ...rest }) => {
                if (!groupedProducts[sku]) {
                    groupedProducts[sku] = { name, totalQty: 0, items: [] };
                }
                groupedProducts[sku].totalQty += quantity;
                groupedProducts[sku].items.push({ product: { sku, name, quantity, ...rest }, orderNum: rest.orderNum });
            });

            generateCollectionDetailsPDF(localCollection, groupedProducts);

        }
        setPrintLoading(false)
        modalRef.current?.close();
    };


    useEffect(() => {
        if (selectedWarehouse?.id && prevWarehouseId.current && selectedWarehouse.id !== prevWarehouseId.current) {
            navigation.goBack();
        }

        if (selectedWarehouse?.id) {
            prevWarehouseId.current = selectedWarehouse.id;
        }
    }, [selectedWarehouse?.id]);

    useFocusEffect(
        useCallback(() => {
            dispatch(fetchOrders({ collectionId, warehouseId: null }));

        }, [dispatch, collectionId])
    );

    const fetchCollection = useCallback(async () => {
        const res = await dispatch(fetchCollectionById(collectionId));

        if (fetchCollectionById.fulfilled.match(res)) {
            dispatch(replaceCollection(res.payload));
            setLocalCollection(res.payload);
        }
    }, [dispatch, collectionId]);

    useFocusEffect(
        useCallback(() => {
            let isActive = true;

            fetchCollection();

            return () => {
                isActive = false; // Prevent setting state if component is unfocused
            };
        }, [dispatch, collectionId])
    );

    const onRefresh = async () => {
        setRefreshing(true);
        //fetch collections to 
        await fetchCollection();
        dispatch(fetchOrders({ collectionId, warehouseId: null }));
        setRefreshing(false);
    };

    const updateStickyHeader = (shouldShow: boolean) => {
        'worklet';
        stickyHeaderOpacity.value = withTiming(shouldShow ? 1 : 0, { duration: 200 });
        stickyHeaderTranslateY.value = withTiming(shouldShow ? 0 : -60, { duration: 200 });
    };

    const updateTabsHeight = (shouldShow: boolean) => {
        'worklet';
        tabsOpacity.value = withTiming(shouldShow ? 1 : 0, { duration: 200 });
        tabsTranslateY.value = withTiming(shouldShow ? 0 : 60, { duration: 200 });
        stickyHeaderHeight.value = withTiming(shouldShow ? 120 : 60, { duration: 200 });
    };

    const handleScroll = (event: any) => {
        const currentScrollY = event.nativeEvent.contentOffset.y;
        scrollY.value = currentScrollY;
        const threshold = buttonsPosition + 50;
        const tabsThreshold = buttonsPosition + 200;
        const shouldTabsShow = currentScrollY >= tabsThreshold;
        const shouldShow = currentScrollY >= threshold;
        runOnJS(updateStickyHeader)(shouldShow);
        runOnJS(updateTabsHeight)(shouldTabsShow);
    };

    const handleButtonsLayout = (event: any) => {
        const { y } = event.nativeEvent.layout;
        setButtonsPosition(y);
    };

    const stickyHeaderAnimatedStyle = useAnimatedStyle(() => {
        return {
            opacity: stickyHeaderOpacity.value,
            transform: [{ translateY: stickyHeaderTranslateY.value }],
            height: stickyHeaderHeight.value,
        };
    });

    const tabsAnimatedStyle = useAnimatedStyle(() => {
        return {
            opacity: tabsOpacity.value,
            transform: [{ translateY: tabsTranslateY.value }],
        };
    });

    const scrollRef1 = useRef<ScrollView>(null);
    const scrollRef2 = useRef<ScrollView>(null);
    const onScroll1 = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const x = event.nativeEvent.contentOffset.x;
        scrollRef2.current?.scrollTo({ x, animated: false });
    };

    const sidebarWidth = useSharedValue(0);
    const sidebarOpacity = useSharedValue(0);

    const animatedSidebarStyle = useAnimatedStyle(() => {
        return {
            width: sidebarWidth.value,
            opacity: sidebarOpacity.value,
        };
    });

    // Function to animate width and then opacity
    const expandSidebar = () => {
        sidebarWidth.value = withTiming(80, { duration: 200 }, (finished) => {
            if (finished) {
                sidebarOpacity.value = withTiming(1, { duration: 200 });
            }
        });
    };

    const collapseSidebar = () => {
        // Fade out first
        sidebarOpacity.value = withTiming(0, { duration: 100 }, (finished) => {
            if (finished) {
                sidebarWidth.value = withTiming(0, { duration: 200 });
            }
        });
    };

    const scrollRef3 = useRef<ScrollView>(null);
    const scrollRef4 = useRef<ScrollView>(null);

    const onScroll3 = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const x = event.nativeEvent.contentOffset.x;

        if (x > 50) {
            expandSidebar(); // Animate width → then opacity
        } else {
            collapseSidebar(); // Animate opacity → then width
        }

        scrollRef4.current?.scrollTo({ x, animated: false });
    };

    const modalRef = useRef<Modalize>(null);
    const modalOrderDetailsRef = useRef<Modalize>(null);
    const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)

    const orderTableHeader = ['Order N', 'Products', 'Tracking Number', 'Shipping Company', 'Contact', 'Store', 'Status'];
    const orderTableWidth = [80, 150, 150, 150, 150, 150, 150];
    const orderTableKeys = ['orderNum', 'goodsDescription', 'trackingNumber', 'shippingCompany', 'contact', 'store', 'status'];

    const tableRenderer = (index: number, item: Order) => {
        const key = orderTableKeys[index];
        const value = item[key];

        if (!value) {
            return (
                <View key={`${key}-${index}`} style={{ width: orderTableWidth[index], alignItems: 'center' }}>
                    <ThemedText variant="body" style={{ fontSize: 12 }}>-</ThemedText>
                </View>
            );
        }

        switch (key) {
            case 'goodsDescription':
                const lines = value.split('<br>');
                const displayedProduct = lines[0];
                const hiddenCount = lines.length - 1;
                return (
                    <View key={`${key}-${index}`} style={{ width: orderTableWidth[index], alignItems: 'center' }}>
                        <ThemedText variant="body" style={{ fontSize: 12 }} numberOfLines={1}>
                            {displayedProduct}
                        </ThemedText>
                        {hiddenCount > 0 && (
                            <ThemedText variant="body" style={{ fontSize: 12 }}>
                                +{hiddenCount} more
                            </ThemedText>
                        )}
                    </View>
                );

            case 'status':
                return (
                    <View key={`${key}-${index}`} style={{ width: orderTableWidth[index], flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
                        <OrderStatus orderStatus={value} />
                    </View>
                );

            default:
                return (
                    <View key={`${key}-${index}`} style={{ width: orderTableWidth[index], alignItems: 'center' }}>
                        <ThemedText style={{ textAlign: 'center' }}>
                            {String(value)}
                        </ThemedText>
                    </View>
                );
        }
    };


    const TableSkeleton = ({ rows = 5 }) => {
        const skeletonRow = (rowIndex: number) => (
            <View key={rowIndex} style={[styles.tableRow, { borderBottomColor: theme.text + '20' }]}>
                {orderTableWidth.map((width, colIndex) => (
                    <Animated.View
                        key={`skeleton-${rowIndex}-${colIndex}`}
                        style={[
                            {
                                width,
                                height: 20,
                                backgroundColor: theme.background,
                                borderRadius: 4,
                                marginRight: 10,
                                opacity: 0.6,
                            },
                            {
                                // animationKey: `pulse-${rowIndex}-${colIndex}`,
                                transform: [{ scale: 1 }],
                            }
                        ]}
                    />
                ))}
            </View>
        );

        return (
            <View style={styles.tableContainer}>
                <ScrollView horizontal style={styles.tableWrapper} showsHorizontalScrollIndicator={false}>
                    <View>
                        <View style={[styles.tableHeader, { borderBottomColor: theme.text + '20' }]}>
                            {Array.from({ length: rows }).map((_, rowIndex) => skeletonRow(rowIndex))}
                        </View>
                        {Array.from({ length: rows }).map((_, rowIndex) => skeletonRow(rowIndex))}
                    </View>
                </ScrollView>
            </View>
        );
    };

    const renderRemain = (index: number) => {
        if (!orders || ![0, 1].includes(index)) return null;

        const targetOrders = orders.filter(order => order.status === (index === 0 ? statusKeys[1] : statusKeys[2]));

        return (
            <View style={{
                marginLeft: 'auto',
                flexDirection: 'row',
                gap: 4,
                alignItems: 'center',
                backgroundColor: theme.success + '20',
                borderRadius: 5,
                paddingHorizontal: 5,
                justifyContent: 'center',
                borderWidth: 1,
                borderColor: theme.success,
            }}>
                <ThemedText
                    variant="subtitle"
                    style={{
                        fontSize: 16,
                        fontWeight: 'bold',
                        color: theme.success,
                    }}
                >
                    {`${targetOrders.length}/${orders.length}`}
                </ThemedText>
            </View>
        );
    };

    return (
        <GlobalLayout title="Collect Details" isInner>
            <ThemedView>
                <Animated.View style={[
                    {
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        zIndex: 1000,
                        backgroundColor: theme.background,
                        borderBottomWidth: 1,
                        borderBottomColor: theme.text + '20',
                        paddingHorizontal: 20,
                        paddingVertical: 8,
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: 12,

                    },
                    stickyHeaderAnimatedStyle
                ]}>
                    <View style={{
                        width: '100%',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: 12
                    }}>

                        {collectionLoading ? <ThreeDotsSkeleton /> : <ThemedText style={{ fontSize: 24 }} variant="title">Num : {localCollection?.num}</ThemedText>}
                        {/* three point of settings */}
                        <TouchableOpacity
                            onPress={() => modalRef.current?.open()}>
                            <Entypo name="dots-three-vertical" size={30} color={theme.text} />
                        </TouchableOpacity>
                    </View>
                    {/* display the tabs and the table header when it come to it  */}
                    <Animated.View style={[tabsAnimatedStyle, {
                        position: 'absolute',
                        top: 60,
                        left: 20,
                        right: 20,
                        zIndex: 1000,
                        opacity: 0
                    }]}>
                        <View style={styles.tabs}>
                            {tabsDef.map((tab, index) => (
                                <TouchableOpacity key={tab.label} onPress={() => setSelectedTab(index)} style={[styles.tab, { backgroundColor: selectedTab === index ? theme.cardBackground : theme.background }]}>
                                    <ThemedText variant={selectedTab === index ? "subtitle" : "body"}>{`${tab.label}`}</ThemedText>
                                    {tab.Count > 0 && <View style={{ backgroundColor: theme.primary, borderRadius: 10, minWidth: 20, minHeight: 20, justifyContent: 'center', alignItems: 'center' }} >
                                        <Text style={{ color: 'white', fontSize: 10 }}>
                                            {`${tab.Count}`}
                                        </Text>

                                    </View>}
                                </TouchableOpacity>
                            ))}
                        </View>
                        {selectedTab === 0 && (
                            <View style={{ position: 'relative' }} >
                                <Animated.View style={[styles.tableHeader, { position: 'absolute', left: 0, top: 0, zIndex: 1000, backgroundColor: theme.cardBackground, borderTopLeftRadius: 10 }, animatedSidebarStyle]}>
                                    <ThemedText
                                        variant='subtitle'
                                        style={{ width: 80, fontWeight: 'bold', textAlign: 'center', }}
                                    >
                                        Order N
                                    </ThemedText>
                                </Animated.View>

                                <ScrollView ref={scrollRef4} horizontal showsHorizontalScrollIndicator={false} style={{ backgroundColor: theme.cardBackground, borderTopLeftRadius: 10, borderTopRightRadius: 10 }}>
                                    <View style={[styles.tableHeader, { paddingHorizontal: 10, }]}>


                                        {orderTableHeader.map((header, index) => (
                                            <ThemedText
                                                variant='subtitle'
                                                key={header}
                                                style={{ width: orderTableWidth[index], fontWeight: 'bold', textAlign: 'center', }}
                                            >
                                                {header}
                                            </ThemedText>
                                        ))}
                                    </View>
                                </ScrollView>
                            </View>

                        )}
                        {selectedTab === 1 && (
                            <ScrollView ref={scrollRef2} horizontal showsHorizontalScrollIndicator={false} style={{ backgroundColor: theme.cardBackground, paddingHorizontal: 10, borderTopLeftRadius: 10, borderTopRightRadius: 10 }}>
                                <View style={[styles.tableHeader, { borderBottomColor: theme.text + '20' }]}>
                                    <ThemedText style={{ width: 80, textAlign: 'left', fontWeight: 'bold' }} variant='subtitle'>Qty</ThemedText>
                                    <ThemedText style={{ width: 150, textAlign: 'left', fontWeight: 'bold' }} variant='subtitle'>SKU</ThemedText>
                                    <ThemedText style={{ width: 200, textAlign: 'center', fontWeight: 'bold' }} variant='subtitle' >Name</ThemedText>
                                </View>
                            </ScrollView>

                        )}


                    </Animated.View>



                </Animated.View>

                <ScrollView
                    ref={scrollViewRef}
                    onScroll={handleScroll}
                    scrollEventThrottle={16}
                    refreshControl={
                        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={theme.primary} />
                    }
                >
                    <View style={{ flex: 1, padding: 20 }}>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>

                            {collectionLoading ? <ThreeDotsSkeleton /> : <ThemedText style={{ fontSize: 24 }} variant="title">Num : {localCollection?.num}</ThemedText>}
                            {/* three point of settings */}
                            <TouchableOpacity style={{ height: 34 }}
                                onPress={() => modalRef.current?.open()}>
                                <Entypo name="dots-three-vertical" size={24} color={theme.text} />
                            </TouchableOpacity>
                        </View>

                        <CollectionDetailsCard loading={collectionLoading || ordersLoading} collection={localCollection} />

                        {/* tab (orders, summary) */}
                        <View style={{ backgroundColor: theme.cardBackground, borderRadius: 10, marginBottom: 10 }}>
                            <View style={styles.tabs}>
                                {tabsDef.map((tab, index) => (
                                    <TouchableOpacity key={tab.label} onPress={() => setSelectedTab(index)} style={[styles.tab, { backgroundColor: selectedTab === index ? theme.cardBackground : theme.background }]}>
                                        <ThemedText variant={selectedTab === index ? "subtitle" : "body"}> {`${tab.label}`}</ThemedText>
                                        {tab.Count > 0 && <View style={{ backgroundColor: theme.primary, borderRadius: 10, minWidth: 20, minHeight: 20, justifyContent: 'center', alignItems: 'center' }} >
                                            {!ordersLoading ? <Text style={{ color: 'white', fontSize: 10 }}>
                                                {`${tab.Count}`}
                                            </Text> : <ActivityIndicator size="small" color="white" />}

                                        </View>}
                                    </TouchableOpacity>
                                ))}

                            </View>
                            {/* table of order display orderNum, goodsDescription , trackingNumber, shippingCompany , contact , store, seller */}
                            {selectedTab === 0 && (
                                ordersLoading ? (
                                    <TableSkeleton rows={6} />
                                ) : (
                                    <View style={styles.tableContainer}>
                                        <Animated.View
                                            style={[
                                                {
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    height: '100%',
                                                    flexDirection: 'column',
                                                    justifyContent: 'flex-start',
                                                    alignItems: 'center',
                                                    backgroundColor: theme.cardBackground,
                                                    zIndex: 10,
                                                    padding: 10,
                                                    borderRadius: 10,
                                                },
                                                animatedSidebarStyle,
                                            ]}
                                        >
                                            <ThemedText
                                                variant='subtitle'
                                                style={{
                                                    fontWeight: 'bold', textAlign: 'left', width: '100%', paddingVertical: 10,
                                                    borderBottomWidth: 1,
                                                }}
                                            >
                                                Order N
                                            </ThemedText>
                                            {orders.map((item, rowIndex) => (
                                                <TouchableOpacity onPress={() => {
                                                    setSelectedOrder(item);
                                                    modalOrderDetailsRef.current?.open();
                                                }} key={rowIndex} style={[styles.tableRow, { borderBottomColor: theme.text + '20' }]}>
                                                    <ThemedText style={{ width: 60, textAlign: 'left' }} variant='body'>#{item.orderNum}</ThemedText>
                                                </TouchableOpacity>
                                            ))}
                                        </Animated.View>
                                        <ScrollView ref={scrollRef3} onScroll={onScroll3} horizontal style={[styles.tableWrapper]} showsHorizontalScrollIndicator={false}>
                                            <View>
                                                <View style={[styles.tableHeader, { borderBottomColor: theme.text + '20' }]}>
                                                    {orderTableHeader.map((header, index) => (
                                                        <ThemedText
                                                            variant='subtitle'
                                                            key={header}
                                                            style={{ width: orderTableWidth[index], fontWeight: 'bold', textAlign: 'center' }}
                                                        >
                                                            {header}
                                                        </ThemedText>
                                                    ))}
                                                </View>

                                                {orders.map((item, rowIndex) => (
                                                    <TouchableOpacity
                                                        onPress={() => {
                                                            setSelectedOrder(item);
                                                            modalOrderDetailsRef.current?.open();
                                                        }}
                                                        key={rowIndex}
                                                        style={[styles.tableRow, { borderBottomColor: theme.text + '20' }]}
                                                    >
                                                        {orderTableKeys.map((_, colIndex) =>
                                                            tableRenderer(colIndex, item)
                                                        )}
                                                    </TouchableOpacity>
                                                ))}
                                            </View>
                                        </ScrollView>
                                    </View>
                                )
                            )}
                            {selectedTab === 1 && (
                                <View style={styles.tableContainer}>
                                    <ScrollView ref={scrollRef1} onScroll={onScroll1} horizontal style={styles.tableWrapper} showsHorizontalScrollIndicator={false}>
                                        <View>
                                            <View style={[styles.tableHeader, { borderBottomColor: theme.text + '20' }]}>
                                                <ThemedText style={{ width: 80, textAlign: 'left', fontWeight: 'bold' }} variant='subtitle'>Qty</ThemedText>
                                                <ThemedText style={{ width: 150, textAlign: 'left', fontWeight: 'bold' }} variant='subtitle'>SKU</ThemedText>
                                                <ThemedText style={{ width: 200, textAlign: 'center', fontWeight: 'bold' }} variant='subtitle' >Name</ThemedText>
                                            </View>
                                            {Object.values(groupedProducts).map((product, index) => (
                                                <View key={index} style={[styles.tableRow, { borderBottomColor: theme.text + '20' }]}>
                                                    <ThemedText style={{ width: 80, textAlign: 'left' }} variant='body'>{product.totalQty} x</ThemedText>

                                                    <ThemedText style={{ width: 150, textAlign: 'left' }} variant='body'>{product.items[0].product.sku}</ThemedText>
                                                    <ThemedText style={{ width: 200, textAlign: 'center' }} variant='body'>{product.name}</ThemedText>
                                                </View>
                                            ))}

                                        </View>
                                    </ScrollView>
                                </View>
                            )}



                        </View>




                    </View>
                </ScrollView>
            </ThemedView>

            <Modalize
                ref={modalRef}
                modalHeight={Dimensions.get('window').height * 0.6}

                modalStyle={[styles.modal, { backgroundColor: theme.cardBackground }]}
                handleStyle={{ backgroundColor: theme.primary }}
                withHandle
            >
                <View style={styles.modalContent}>
                    {/* Header */}
                    <View style={styles.headerModal}>
                        <ThemedText variant="title" style={styles.modalTitle}>
                            Options
                        </ThemedText>
                        {/* edit pencil */}

                    </View>
                </View>
                <View style={{ paddingHorizontal: 20, gap: 4 }}>
                    <TouchableOpacity onPress={() => GetExcel(localCollection, orders)} style={[styles.option]}>
                        <MaterialCommunityIcons name="file-excel" size={24} color={theme.text} />
                        <ThemedText variant="body" style={styles.optionText}>
                            Export Excel
                        </ThemedText>
                    </TouchableOpacity>
                    <TouchableOpacity style={[styles.option]} onPress={printCollect} >
                        <Entypo name="print" size={24} color={theme.text} />
                        <ThemedText variant="body" style={styles.optionText}>
                            Print Collect
                        </ThemedText>
                        {PrintCollectLoading && <ActivityIndicator size="small" color={theme.primary} />}
                    </TouchableOpacity>
                    <TouchableOpacity style={[styles.option]} onPress={printCollectionSummary}>
                        <MaterialIcons name="summarize" size={24} color={theme.text} />
                        <ThemedText variant="body" style={styles.optionText}>
                            Print Summary
                        </ThemedText>
                        {PrintLoading && <ActivityIndicator size="small" color={theme.primary} />}
                    </TouchableOpacity>
                    <TouchableOpacity style={[styles.option]}>
                        <FontAwesome name="print" size={24} color={theme.text} />
                        <ThemedText variant="body" style={styles.optionText}>
                            Print Order Labels
                        </ThemedText>
                    </TouchableOpacity>

                    {!localCollection?.useBulk && localCollection?.status !== statusKeys[2] && (
                        <>
                            <View style={{ backgroundColor: theme.text + '40', height: 1, width: '100%' }} />
                            <TouchableOpacity onPress={() => {


                                if (localCollection?.status !== statusKeys[2] && !orders.every(order => order.status === statusKeys[1])) {
                                    navigation.navigate('Fulfillment', { collectionId: collectionId, orders, action: 'fullfillment' });
                                    modalRef.current?.close();
                                } else if (orders.every(order => order.status === statusKeys[1])) {
                                    showToast('Collection already fulfilled', 'info');
                                } else {
                                    showToast('Collection already shipped', 'info');
                                }

                            }} style={[styles.option]}>
                                <MaterialCommunityIcons name="package" size={24} color={theme.text} />
                                <ThemedText variant="body" style={styles.optionText}>
                                    Start Fulfilement
                                </ThemedText>
                                {renderRemain(0)}
                            </TouchableOpacity>

                            <TouchableOpacity onPress={() => {
                                //check if any of the orders is not fullfilled
                                const hasUnFullfilled = orders.some(order => order.status !== statusKeys[1]);
                                if (hasUnFullfilled) {
                                    showToast('Please complete fulfilment before shipping', 'info');
                                    return;
                                }
                                modalRef.current?.close();
                                navigation.navigate('Fulfillment', { collectionId: collectionId, orders, action: 'shippement' });
                            }}
                                style={[styles.option]}>
                                <FontAwesome6 name="truck-ramp-box" size={24} color={theme.text} />
                                <ThemedText variant="body" style={styles.optionText}>
                                    Start Shipping
                                </ThemedText>
                                {renderRemain(1)}
                            </TouchableOpacity>
                        </>
                    )}

                    {localCollection?.useBulk && localCollection?.status !== statusKeys[2] && (
                        <><View style={{ backgroundColor: theme.text + '40', height: 1, width: '100%' }} />
                            <TouchableOpacity onPress={() => {
                                if (localCollection?.status !== statusKeys[2]) {
                                    modalRef.current?.close();
                                    navigation.navigate('Fulfillment', { collectionId: collectionId, orders, action: 'shippement' });

                                } else {
                                    showToast('Collection already shipped', 'info');
                                }

                            }} style={[styles.option]}>
                                <MaterialCommunityIcons name="package" size={24} color={theme.text} />
                                <ThemedText variant="body" style={styles.optionText}>
                                    Start Fulfilement & Shipping
                                </ThemedText>
                                {/* {renderRemain(0)} */}
                            </TouchableOpacity></>)}

                    {
                        localCollection?.status === statusKeys[2] && localCollection?.confirmation?.deliverySignedAt && (
                            <><View style={{ backgroundColor: theme.text + '40', height: 1, width: '100%' }} />
                                <TouchableOpacity onPress={() => {
                                    navigation.navigate('Acknowledgment', { collectionId, orders });
                                }} style={[styles.option]}>
                                    <FontAwesome5 name="file-signature" size={24} color={theme.text} />
                                    <ThemedText variant="body" style={styles.optionText}>
                                        Print Acknowledgment
                                    </ThemedText>
                                </TouchableOpacity></>)

                    }
                    {
                        localCollection?.status === statusKeys[2] && !localCollection?.confirmation && (
                            <><View style={{ backgroundColor: theme.text + '40', height: 1, width: '100%' }} />
                                <TouchableOpacity onPress={() => {
                                    navigation.navigate('Acknowledgment', { collectionId, orders });
                                }} style={[styles.option]}>
                                    <FontAwesome5 name="file-signature" size={24} color={theme.text} />
                                    <ThemedText variant="body" style={styles.optionText}>
                                        Add Confirmation
                                    </ThemedText>
                                </TouchableOpacity></>)

                    }

                </View>
            </Modalize>

            <Modalize
                ref={modalOrderDetailsRef}
                modalHeight={Dimensions.get('window').height * 0.8}
                modalStyle={[styles.modal, { backgroundColor: theme.cardBackground }]}
                handleStyle={{ backgroundColor: theme.primary }}
                withHandle
            >
                <View style={styles.modalContent}>
                    {/* Header */}
                    <View style={{
                        flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', width: '100%'
                        , backgroundColor: theme.primary + '20', borderRadius: 10, borderColor: theme.text + '20', borderWidth: 1
                    }}>
                        <View style={{ flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', padding: 20 }}>
                            {selectedOrder?.orderCode &&
                                <BarcodeView value={selectedOrder?.orderCode} format="CODE128" color={theme.text} bgColor='transparent' maxWidth={200} height={70} />}
                            <ThemedText variant="body" style={[styles.modalOrderText]}>
                                {selectedOrder?.orderCode}
                            </ThemedText>
                        </View>
                        <ThemedText variant="title" style={styles.modalTitle}>
                            Order #{selectedOrder?.orderNum}
                        </ThemedText>
                        <View style={{ height: 1, backgroundColor: theme.text + '20', width: '100%' }} />
                        <View style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 10, justifyContent: 'flex-start', alignItems: 'flex-start', paddingVertical: 20 }}>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%', paddingHorizontal: 20, }}>
                                <ThemedText>Status</ThemedText>
                                <OrderStatus orderStatus={selectedOrder?.status || ''} />
                            </View>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%', paddingHorizontal: 20, }}>
                                <ThemedText>Created</ThemedText>
                                <ThemedText>{dayjs(selectedOrder?.createdAt).format('DD/MM/YYYY')}</ThemedText>
                            </View>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%', paddingHorizontal: 20, }}>
                                <ThemedText>Tracking Number</ThemedText>
                                <ThemedText>{selectedOrder?.trackingNumber}</ThemedText>
                            </View>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%', paddingHorizontal: 20, }}>
                                <ThemedText>Shipping Company</ThemedText>
                                <ThemedText>{selectedOrder?.shippingCompany}</ThemedText>
                            </View>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: '100%', paddingHorizontal: 20, }}>
                                <ThemedText>Contact</ThemedText>
                                <ThemedText>{selectedOrder?.contact}</ThemedText>
                            </View>
                        </View>
                    </View>

                    <View style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 10, justifyContent: 'flex-start', alignItems: 'flex-start', paddingVertical: 20 }}>
                        <ThemedText variant="title" style={{ fontSize: 18, fontWeight: '600', }}>
                            Products
                        </ThemedText>
                    </View>
                    <View style={styles.tableContainer}>
                        <ScrollView horizontal style={styles.tableWrapper} showsHorizontalScrollIndicator={false}>
                            <View >
                                <View style={[styles.tableHeader, { borderBottomColor: theme.text + '20' }]}>

                                    <ThemedText style={{ width: 80, textAlign: 'left' }} variant='subtitle'>Qty</ThemedText>
                                    <ThemedText style={{ width: 200, textAlign: 'left' }} variant='subtitle' >Name</ThemedText>
                                    <ThemedText style={{ width: 150, textAlign: 'left' }} variant='subtitle'>SKU</ThemedText>
                                </View>
                                {selectedOrder?.ordersProducts.map((product: OrderProduct, index: number) => (
                                    <View style={[styles.tableRow, { borderBottomColor: theme.text + '20' }]} key={index}>

                                        <ThemedText style={{ width: 80, textAlign: 'left' }} variant='body'>{product.quantity} x</ThemedText>
                                        <ThemedText style={{ width: 200, textAlign: 'left' }} variant='body'>{product.name}</ThemedText>
                                        <ThemedText style={{ width: 150, textAlign: 'left' }} variant='body'>{product.sku}</ThemedText>
                                    </View>
                                ))}

                            </View>
                        </ScrollView>
                    </View>

                </View>

            </Modalize>
        </GlobalLayout >
    );
}

const styles = StyleSheet.create({
    modal: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    modalContent: {
        padding: 20,
    },
    modalTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    headerSection: {
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        paddingVertical: 16,
    },
    headerModal: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
    },
    editButton: {
        padding: 8,

    },
    modalOrderText: {
        fontSize: 16,
    },
    option: {
        width: '100%',
        padding: 10,
        display: 'flex',
        justifyContent: 'flex-start',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
        height: 50,
        borderRadius: 10,

    },
    optionText: {
        fontSize: 16,
    },
    tabs: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: Dimensions.get('window').width - 40,
        borderRadius: 10,
        overflow: 'hidden',
        padding: 5,
    },
    tab: {
        padding: 5,
        display: 'flex',
        flex: 1,
        height: '100%',
        width: (Dimensions.get('window').width - 70) / 2,
        flexDirection: 'row',
        gap: 10,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
    },
    tableContainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        width: '100%',

    },
    tableWrapper: {
        width: '100%',
        padding: 10,
        borderRadius: 10,
    },
    tableBody: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
    },
    tableHeader: {
        flexDirection: 'row',
        paddingVertical: 10,
        borderBottomWidth: 1,

    },
    tableRow: {
        flexDirection: 'row',
        paddingVertical: 10,
        borderBottomWidth: 1,
        height: 50,
    },
})