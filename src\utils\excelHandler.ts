import XLSX from "xlsx-js-style";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import { Order, Collection } from "../navigation/type";
import dayjs from "dayjs";

export async function GetExcel(collection: Collection | null, orders: Order[]) {
    try {
        if (collection) {
            await generateStyledCollectionExcel(collection, orders);
        }
    } catch (err) {
        console.error('Excel export error:', err);
    }
}


export async function generateStyledCollectionExcel(collection: Collection, orders: Order[]) {
    const wb = XLSX.utils.book_new();

    const s_header = {
        font: { name: "Cal<PERSON>ri", sz: 10, bold: true, color: { rgb: "ffffff" } },
        fill: { fgColor: { rgb: "111827" } },
        alignment: { wrapText: true, vertical: "center", horizontal: "center" },
        border: {
            left: { style: "thick", color: { rgb: "111827" } },
            top: { style: "thick", color: { rgb: "111827" } },
            bottom: { style: "thick", color: { rgb: "111827" } },
            right: { style: "thick", color: { rgb: "111827" } },
        },
    };
    const s_p_header = {
        font: { name: "Calibri", sz: 10, bold: true, color: { rgb: "ffffff" } },
        fill: { fgColor: { rgb: "1E293B" } },
        alignment: { wrapText: true, vertical: "center", horizontal: "center" },
        border: {
            left: { style: "thick", color: { rgb: "1E293B" } },
            top: { style: "thick", color: { rgb: "1E293B" } },
            bottom: { style: "thick", color: { rgb: "1E293B" } },
            right: { style: "thick", color: { rgb: "1E293B" } },
        },
    };

    const s_p_row = {
        font: { name: "Calibri", sz: 12 },
        alignment: { vertical: "center", horizontal: "center" },
        border: {
            left: { style: "thin", color: { rgb: "111827" } },
            top: { style: "thin", color: { rgb: "111827" } },
            bottom: { style: "thin", color: { rgb: "111827" } },
            right: { style: "thin", color: { rgb: "111827" } },
        },
    };
    const s_row = {
        font: { name: "Calibri", sz: 12 },
        fill: { fgColor: { rgb: "D9D9D9" } },
        alignment: { vertical: "center", horizontal: "center" },
        border: {
            left: { style: "thin", color: { rgb: "111827" } },
            top: { style: "thin", color: { rgb: "111827" } },
            bottom: { style: "thin", color: { rgb: "111827" } },
            right: { style: "thin", color: { rgb: "111827" } },
        },
    };

    const ws: XLSX.WorkSheet = {};

    // Title in C2:D2
    ws["B2"] = {
        v: "Collection Details",
        t: "s",
        s: s_header,
    };

    ws["!merges"] = [
        { s: { r: 1, c: 1 }, e: { r: 1, c: 3 } }, // Merge C2:D2
    ];

    // Detail rows start at row 4
    const details = [
        ["Number", collection.num],
        ["Status", collection.status],
        ["Shipping Type", collection.shippingType],
        ["Shipping By", collection.shippingBy],
        ["Added At", dayjs(collection.addedAt).format("DD/MM/YYYY")],
    ];

    for (let i = 0; i < details.length; i++) {
        const row = i + 3; // Excel row
        ws["!merges"]!.push({ s: { r: row - 1, c: 2 }, e: { r: row - 1, c: 3 } }); // Cx:Dx

        ws[`B${row}`] = { v: details[i][0], t: "s", s: s_row }; // label
        ws[`C${row}`] = { v: details[i][1], t: "s", s: s_row }; // value
        ws[`D${row}`] = { t: "s", s: s_row }; // ensure border visible on merged cell
    }

    // Add "Orders" section header
    const orderStartRow = 3 + details.length + 2; // Leave one blank row
    ws[`B${orderStartRow}`] = { v: "Products", t: "s", s: s_p_header };
    ws["!merges"]!.push({ s: { r: orderStartRow - 1, c: 1 }, e: { r: orderStartRow - 1, c: 3 } });

    let currentRow = orderStartRow;

    // Product Headers
    const productHeaders = ["Qty", "SKU", "Name"];
    for (let i = 0; i < productHeaders.length; i++) {
        ws[XLSX.utils.encode_cell({ r: currentRow, c: i + 1 })] = {
            v: productHeaders[i],
            t: "s",
            s: s_p_header,
        };
    }
    currentRow++;
    // Order Headers
    const orderHeaders = ["Order #", "Tracking Number", "Shipping Company", "Contact", "Store", "Status"];
    for (let i = 0; i < orderHeaders.length; i++) {
        ws[XLSX.utils.encode_cell({ r: currentRow, c: i })] = {
            v: orderHeaders[i],
            t: "s",
            s: s_header,
        };
    }
    currentRow++;

    for (const order of orders) {

        const orderValues = [
            order.orderNum || "-",
            order.trackingNumber || "-",
            order.shippingCompany || "-",
            order.contact || "-",
            order.store || "-",
            order.status || "-",
        ];
        for (let i = 0; i < orderValues.length; i++) {
            ws[XLSX.utils.encode_cell({ r: currentRow, c: i })] = {
                v: orderValues[i],
                t: "s",
                s: s_row,
            };
        }
        currentRow++;



        for (const product of order.ordersProducts) {
            const productValues = [
                product.quantity || "-",
                product.sku || "-",
                product.name || "-",
            ];
            for (let i = 0; i < productValues.length; i++) {
                ws[XLSX.utils.encode_cell({ r: currentRow, c: i + 1 })] = {
                    v: productValues[i],
                    t: "s",
                    s: s_p_row,
                };
            }
            currentRow++;
        }

    }

    // Auto column width
    ws["!cols"] = Array(6).fill({ wch: 25 });

    // Set sheet ref
    ws["!ref"] = XLSX.utils.encode_range({
        s: { r: 0, c: 0 },
        e: { r: currentRow, c: 5 },
    });

    // Export
    XLSX.utils.book_append_sheet(wb, ws, "Collection");
    const wbout = XLSX.write(wb, { type: "base64", bookType: "xlsx" });
    const fileUri = FileSystem.cacheDirectory + `Collection_${collection.num}.xlsx`;

    await FileSystem.writeAsStringAsync(fileUri, wbout, {
        encoding: FileSystem.EncodingType.Base64,
    });

    await Sharing.shareAsync(fileUri, {
        mimeType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        dialogTitle: "Export Collection Excel",
        UTI: "com.microsoft.excel.xlsx",
    });
}
