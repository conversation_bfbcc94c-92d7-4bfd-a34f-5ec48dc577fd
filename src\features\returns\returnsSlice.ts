import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Return, ReturnReason } from '../../navigation/type';
import axiosClient from '../../api/axiosClient';

// Define the shape of the slice state
interface ReturnsState {
    returns: Return[];
    reasons: ReturnReason[];
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: ReturnsState = {
    returns: [],
    reasons: [],
    loading: false,
    error: null,
};



// Async thunk to fetch returns
export const fetchReturns = createAsyncThunk(
    'returns/fetchReturns',
    async (warehouseId: number, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get(`/returns?warehouse_id=${warehouseId}`);

            return response.data.result;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch orders');
        }
    }
);

// Async thunk to add a new return
export const addReturn = createAsyncThunk(
    'returns/addReturn',
    async (formData: FormData, { rejectWithValue, getState }) => {
        try {
            console.log('formData', formData);

            const response = await axiosClient.post('/returns', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message);
            }
            return response.data.result;

        } catch (error: any) {
            // Catch 422 validation error
            if (error.response && error.response.data) {
                const { message, errors } = error.response.data;

                // Option 1: return just the top-level message
                return rejectWithValue(message);
            }

            return rejectWithValue('Failed to add return');
        }
    }
);

export const getReasons = createAsyncThunk(
    'returns/getReasons',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get('/returns/reasons');
            return response.data.result;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to get reasons');
        }
    }
);

// Async thunk to update a return
export const updateReturn = createAsyncThunk(
    'returns/updateReturn',
    async ({ id, formData }: { id: number; formData: FormData }, { rejectWithValue }) => {
        try {

            const response = await axiosClient.post(`/returns/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message);
            }
            return { id, updates: response.data.result };

        } catch (error: any) {
            // Catch 422 validation error
            if (error.response && error.response.data) {
                const { message, errors } = error.response.data;

                // Option 1: return just the top-level message
                return rejectWithValue(message);
            }
            return rejectWithValue('Failed to update return');
        }
    }
);

// Async thunk to delete a return
export const deleteReturn = createAsyncThunk(
    'returns/deleteReturn',
    async (id: number, { rejectWithValue }) => {
        try {
            const response = await axiosClient.delete(`/returns/${id}`);
            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message);
            }
            return id;
        } catch (error: any) {
            return rejectWithValue('Failed to delete return');
        }
    }
);

// Slice
const returnsSlice = createSlice({
    name: 'returns',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            // Fetch returns
            .addCase(fetchReturns.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchReturns.fulfilled, (state, action: PayloadAction<Return[]>) => {
                state.loading = false;
                state.returns = action.payload;
            })
            .addCase(fetchReturns.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            // Add return
            .addCase(addReturn.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(addReturn.fulfilled, (state, action: PayloadAction<Return>) => {
                state.loading = false;
                state.returns.push(action.payload);
            })
            .addCase(addReturn.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            // Update return
            .addCase(updateReturn.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateReturn.fulfilled, (state, action: PayloadAction<{ id: number; updates: Partial<Return> }>) => {
                state.loading = false;
                const { id, updates } = action.payload;
                const index = state.returns.findIndex(r => r.id === id);
                if (index !== -1) {
                    state.returns[index] = { ...state.returns[index], ...updates };
                }
            })
            .addCase(updateReturn.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            // Delete return
            .addCase(deleteReturn.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteReturn.fulfilled, (state, action: PayloadAction<number>) => {
                state.loading = false;
                state.returns = state.returns.filter(r => r.id !== action.payload);
            })
            .addCase(deleteReturn.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(getReasons.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getReasons.fulfilled, (state, action: PayloadAction<ReturnReason[]>) => {
                state.loading = false;
                state.reasons = action.payload;
            })
            .addCase(getReasons.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            });
    },
});

export const { clearError } = returnsSlice.actions;
export default returnsSlice.reducer;
