// utils/axiosClient.ts
import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { store } from '../app/store';
import { logout } from '../features/auth/authSlice';
import { clearAuthData } from '../utils/authStorage';
import { logoutUser } from '../features/auth/authThunks';

const axiosClient = axios.create({
    baseURL: 'http://192.168.1.78:8000/api/v1',
});

// Inject token and default headers before requests
axiosClient.interceptors.request.use(async (config) => {
    const token = await SecureStore.getItemAsync('authToken');

    // Set default Content-Type only if not already set
    if (!config.headers['Content-Type']) {
        config.headers['Content-Type'] = 'application/json';
    }

    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
});

// Handle 401 unauthorized responses
axiosClient.interceptors.response.use(
    (response) => response,
    async (error) => {
        if (error.response?.status === 401) {
            await clearAuthData();
            store.dispatch(logoutUser());
        }
        return Promise.reject(error);
    }
);

export default axiosClient;
