import React, { useState, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, Dimensions, Modal, Pressable } from 'react-native';
import { Entypo } from '@expo/vector-icons';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

type MenuItem = {
    label: string;
    onPress: () => void;
    color?: string;
};

type Props = {
    items: MenuItem[];
    iconColor?: string;
};

export default function ThreeDotsMenu({ items, iconColor }: Props) {
    const theme = useTheme();
    const [visible, setVisible] = useState(false);
    const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
    const buttonRef = useRef<TouchableOpacity>(null);

    const showMenu = () => {
        if (buttonRef.current) {
            buttonRef.current.measure((fx, fy, width, height, px, py) => {
                // Calculate menu position
                const menuWidth = 150;
                const menuHeight = items.length * 50;
                
                let x = px - menuWidth + width;
                let y = py + height;
                
                // Adjust if menu would go off screen
                if (x < 10) x = 10;
                if (x + menuWidth > screenWidth - 10) x = screenWidth - menuWidth - 10;
                if (y + menuHeight > screenHeight - 100) y = py - menuHeight;
                
                setMenuPosition({ x, y });
                setVisible(true);
            });
        }
    };

    const hideMenu = () => {
        setVisible(false);
    };

    const handleItemPress = (onPress: () => void) => {
        hideMenu();
        onPress();
    };

    return (
        <>
            <TouchableOpacity
                ref={buttonRef}
                onPress={showMenu}
                style={styles.button}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
                <Entypo 
                    name="dots-three-vertical" 
                    size={20} 
                    color={iconColor || theme.text} 
                />
            </TouchableOpacity>

            <Modal
                visible={visible}
                transparent
                animationType="fade"
                onRequestClose={hideMenu}
            >
                <Pressable style={styles.overlay} onPress={hideMenu}>
                    <View 
                        style={[
                            styles.menu, 
                            { 
                                backgroundColor: theme.cardBackground,
                                left: menuPosition.x,
                                top: menuPosition.y,
                            }
                        ]}
                    >
                        {items.map((item, index) => (
                            <TouchableOpacity
                                key={index}
                                style={[
                                    styles.menuItem,
                                    index < items.length - 1 && { borderBottomColor: theme.border, borderBottomWidth: 1 }
                                ]}
                                onPress={() => handleItemPress(item.onPress)}
                            >
                                <ThemedText 
                                    style={[
                                        styles.menuItemText,
                                        item.color && { color: item.color }
                                    ]}
                                >
                                    {item.label}
                                </ThemedText>
                            </TouchableOpacity>
                        ))}
                    </View>
                </Pressable>
            </Modal>
        </>
    );
}

const styles = StyleSheet.create({
    button: {
        padding: 8,
    },
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    menu: {
        position: 'absolute',
        minWidth: 150,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    menuItem: {
        paddingVertical: 15,
        paddingHorizontal: 20,
    },
    menuItemText: {
        fontSize: 16,
    },
});
