import { createSlice, PayloadAction } from '@reduxjs/toolkit';

type ModalId = string;

type OpenPayload = { id: ModalId; payload?: any };

type ModalsState = {
    stack: ModalId[];                    // keeps open order (topmost = last)
    payloads: Record<ModalId, any>;      // optional per-modal payload
};

const initialState: ModalsState = { stack: [], payloads: {} };

const modalsSlice = createSlice({
    name: 'modals',
    initialState,
    reducers: {
        openModal: (state, action: PayloadAction<OpenPayload>) => {
            const { id, payload } = action.payload;
            if (!state.stack.includes(id)) state.stack.push(id);
            if (payload !== undefined) state.payloads[id] = payload;
        },
        closeModal: (state, action: PayloadAction<ModalId>) => {
            const id = action.payload;
            state.stack = state.stack.filter(x => x !== id);
            delete state.payloads[id];
        },
        closeTopModal: (state) => {
            const id = state.stack.pop();

            if (id) delete state.payloads[id];
        },
        closeAllModals: (state) => {
            state.stack = [];
            state.payloads = {};
        },
    },
});

export const { openModal, closeModal, closeTopModal, closeAllModals } = modalsSlice.actions;
export default modalsSlice.reducer;

// Selectors
export const selectIsModalOpen = (id: string) => (state: any) => state.modals.stack.includes(id);
export const selectTopModalId = (state: any) => state.modals.stack[state.modals.stack.length - 1] ?? null;
export const selectModalPayload = (id: string) => (state: any) => state.modals.payloads[id];
