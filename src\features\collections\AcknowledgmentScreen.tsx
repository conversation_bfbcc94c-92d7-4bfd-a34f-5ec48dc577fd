import React, { useEffect, useRef, useState } from 'react';
import {
    View,
    StyleSheet,
    ScrollView,
    Dimensions,
    TouchableOpacity,
    Image,
    Pressable,
    ActivityIndicator,
} from 'react-native';
import { BlurView } from 'expo-blur';
import SignatureScreen from 'react-native-signature-canvas';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../app/store';
import { Collection, CollectionStackParamList } from '../../navigation/type';
import dayjs from 'dayjs';
import GlobalLayout from '../../components/GlobalLayout';
import { setConfirmationDelivery } from './CollectionsThunks';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { generateAcknowledgmentHtml } from '../../utils/pdfGenerator';
import * as Print from 'expo-print';
import { FontAwesome5 } from '@expo/vector-icons';

type AcknowledgmentRouteProp = RouteProp<CollectionStackParamList, 'Acknowledgment'>;

export default function AcknowledgmentScreen() {
    const theme = useTheme();
    const { params } = useRoute<AcknowledgmentRouteProp>();
    const navigation = useNavigation<NativeStackNavigationProp<CollectionStackParamList, 'Acknowledgment'>>();
    const { collectionId, orders } = params;
    const { collections } = useSelector((state: RootState) => state.collections);
    const { statusKeys } = useSelector((state: RootState) => state.orders);
    const dispatch = useDispatch<AppDispatch>();
    const [collection, setCollection] = useState<Collection | null>(null);
    const [showSignature, setShowSignature] = useState(false);
    const [signatureBase64, setSignatureBase64] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    //get user
    const { user } = useSelector((state: RootState) => state.auth);


    const ref = useRef<any>(null);

    useEffect(() => {
        if (collections.length > 0) {

            const col = collections.find(col => col.id === collectionId);
            if (col) {
                if (col.confirmation?.recipient?.fullName) {

                    setSignatureBase64(col.confirmation?.recipient?.signature || null);
                } else {
                    console.log('No recipient found');

                    navigation.navigate('Fulfillment', { collectionId, orders, action: 'shippement' });
                }
                setCollection(col);
            }
        }

    }, [collections]);

    const handleSignature = (signature: string) => {
        setSignatureBase64(signature);
        setShowSignature(false);
        setIsLoading(false);
        // TODO: Dispatch to Redux or send to API
    };

    const handleEmpty = () => {
        setShowSignature(false);
        setIsLoading(false);
    };

    const handleClear = () => {
        console.log('Signature cleared');
    };

    const handleError = (err: any) => {
        console.error('Signature pad error:', err);
        setIsLoading(false);
    };

    const handleEnd = () => {
        console.log('Signature drawing ended');
    };

    const handleSubmit = async () => {
        if (collection?.confirmation?.recipient?.fullName) {
            setIsLoading(true);
            const res = await dispatch(setConfirmationDelivery({ collectionId, receiver: collection.confirmation.recipient.fullName, signature: signatureBase64 }));
            if (setConfirmationDelivery.fulfilled.match(res)) {
                setIsLoading(false);
                // navigation.navigate('CollectionDetails', { collectionId });
            }
        }
    };

    const [isPrintLoading, setIsPrintLoading] = useState(false);

    const handlePrint = async () => {
        setIsPrintLoading(true);
        const html = generateAcknowledgmentHtml({
            collection: collection,
            orders: orders,
            signatureBase64: signatureBase64,
            logoUrl: user?.logo,
            companyName: user?.tenant_name,
        });

        await Print.printAsync({
            html,
        });
        setIsPrintLoading(false);
    };


    return (
        <GlobalLayout title="Acknowledgment" isInner>
            {showSignature && (
                <View style={styles.signatureOverlay}>
                    <View style={styles.signatureWrapper}>
                        <SignatureScreen
                            ref={ref}
                            onOK={handleSignature}
                            onEmpty={handleEmpty}
                            onClear={handleClear}
                            onEnd={handleEnd} // just log, don't submit
                            onError={handleError}
                            autoClear={false}
                            descriptionText="Sign here"
                            clearText="Clear"
                            confirmText="Save"
                            penColor="#000"
                            backgroundColor="#fff"
                            webStyle={`.m-signature-pad--footer { display: none; }`}
                        />
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', padding: 10, backgroundColor: '#fff' }}>
                            <TouchableOpacity onPress={() => ref.current?.clearSignature()}>
                                <ThemedText style={{ color: theme.error, fontWeight: 'bold' }}>Clear</ThemedText>
                            </TouchableOpacity>
                            <TouchableOpacity
                                onPress={() => {
                                    setIsLoading(true);
                                    ref.current?.readSignature();
                                    // handleSubmit();
                                }}
                            >
                                <ThemedText style={{ color: theme.primary, fontWeight: 'bold' }}>OK</ThemedText>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            )}


            <ScrollView contentContainerStyle={styles.container}>
                <View style={styles.card}>
                    <ThemedText variant="title" style={{ marginBottom: 16, color: '#000' }}>Delivery Acknowledgment</ThemedText>

                    <ThemedText variant="body" style={styles.agreement}>
                        {`I, ${collection?.confirmation?.recipient?.fullName || '[Delivery Name]'}, hereby acknowledge that I have received all the items listed under Collection No. ${collection?.num}, prepared by the warehouse team of [Company Name] on ${dayjs().format('DD/MM/YYYY')}.`}
                    </ThemedText>

                    <ThemedText variant="body" style={[styles.agreement, { textAlign: 'center' }]}>I confirm that:</ThemedText>

                    <ThemedText variant="body" style={[styles.agreement, { marginLeft: 10 }]}>
                        • I have reviewed and verified the following orders included in this collection:
                    </ThemedText>

                    {orders.map((order, index) => (
                        <ThemedText key={order.id} variant="body" style={[styles.agreement, { marginLeft: 20 }]}>
                            {`${index + 1}. ${order.orderNum} - ${order.orderCode}`}
                        </ThemedText>
                    ))}

                    <ThemedText variant="body" style={[styles.agreement, { marginLeft: 10 }]}>
                        • The items are complete and in acceptable condition at the time of receipt.
                    </ThemedText>

                    <ThemedText variant="body" style={[styles.agreement, { marginLeft: 10 }]}>
                        • I take full responsibility for delivering the orders to the intended recipients as listed in the associated documents.
                    </ThemedText>

                    <ThemedText variant="body" style={[styles.agreement, { marginLeft: 10 }]}>
                        • I understand that any loss, damage, or discrepancy during delivery may result in an internal investigation and possible accountability.
                    </ThemedText>
                    <ThemedText variant="body" style={styles.agreement}>
                        {`Signed electronically on ${dayjs().format('DD/MM/YYYY')}, by:`}
                    </ThemedText>
                    <View style={{ flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', marginBottom: 10 }}>

                        <ThemedText variant="body" style={styles.agreement}>
                            Recipient : {collection?.confirmation?.recipient?.fullName || '[Recipient Name]'}
                        </ThemedText>
                        {signatureBase64 && (
                            <View style={[styles.preview, { borderColor: theme.text + '20' }]}>
                                <TouchableOpacity onPress={() => { setShowSignature(true); setSignatureBase64(null) }} disabled={isLoading}
                                    style={{ position: 'absolute', top: -10, right: -10, zIndex: 1, backgroundColor: theme.primary + '60', padding: 10, borderRadius: 10, }}>
                                    <FontAwesome5 name="file-signature" size={24} color={theme.text} />
                                </TouchableOpacity>
                                <Image
                                    source={{ uri: signatureBase64 }}
                                    style={{ height: 150, width: 150, borderRadius: 6 }}
                                    resizeMode="contain"
                                />
                            </View>
                        )}

                    </View>


                </View>





            </ScrollView>

            <View style={[styles.fixedBlur, { backgroundColor: theme.primary + '60' }]}>
                <BlurView experimentalBlurMethod="none" intensity={50} style={{
                    flexDirection: 'row', alignItems: 'center', justifyContent: 'space-evenly', flex: 1, paddingHorizontal: 10, gap: 10, width: '100%',
                }} >
                    {
                        signatureBase64 ?
                            <>

                                {signatureBase64 !== collection?.confirmation?.recipient?.signature ?
                                    <TouchableOpacity disabled={isLoading}
                                        style={{ flex: 1, height: 50, borderRadius: 40, justifyContent: 'center', alignItems: 'center' }}
                                        onPress={() => handleSubmit()}
                                    >
                                        {isLoading ? (
                                            <ActivityIndicator size="small" color={theme.text} />
                                        ) : (<>
                                            <FontAwesome5 name="save" size={24} color={theme.text} />
                                            <ThemedText style={{ color: theme.text }}>Save</ThemedText></>)}
                                    </TouchableOpacity>
                                    :
                                    <TouchableOpacity disabled={isPrintLoading}
                                        style={{ flex: 1, height: 50, borderRadius: 40, justifyContent: 'center', alignItems: 'center' }}
                                        onPress={() => handlePrint()}
                                    >
                                        {isPrintLoading ? (
                                            <ActivityIndicator size="small" color={theme.text} />
                                        ) : (
                                            <>
                                                <FontAwesome5 name="print" size={24} color={theme.text} />
                                                <ThemedText style={{ color: theme.text }}>Print </ThemedText>
                                            </>)}
                                    </TouchableOpacity>
                                }
                            </> :
                            <TouchableOpacity
                                style={{ flex: 1, height: 50, borderRadius: 40, justifyContent: 'center', alignItems: 'center' }}
                                onPress={() => setShowSignature(true)}
                            >
                                <FontAwesome5 name="file-signature" size={24} color={theme.text} />
                                <ThemedText style={{ color: theme.text }}>Agree & Sign</ThemedText>
                            </TouchableOpacity>}







                </BlurView >
            </View>


        </GlobalLayout>
    );
}

const styles = StyleSheet.create({
    fixedBlur: {
        position: 'absolute',
        bottom: 20,
        left: '10%',
        right: '10%',
        zIndex: 999,
        height: 70,
        borderRadius: 40,
        overflow: 'hidden',
    },
    container: {
        padding: 20,
        paddingBottom: 100,
    },
    card: {
        width: '100%',
        borderRadius: 5,
        padding: 16,
        backgroundColor: '#fff',
    },
    agreement: {
        color: '#333',
        marginBottom: 16,
        fontSize: 14,
        lineHeight: 20,
    },
    button: {
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignSelf: 'flex-end',
        marginTop: 16,
    },
    printButton: {
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 8,
        marginTop: 16,
        flexDirection: 'row',
        gap: 10,
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
    },
    preview: {
        position: 'relative',
        marginTop: 10,
        padding: 10,
        borderRadius: 10,
        borderWidth: 1,
    },
    signatureOverlay: {
        position: 'absolute',
        top: 0, bottom: 0, left: 0, right: 0,
        zIndex: 9999,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    signatureWrapper: {
        width: Dimensions.get('window').width * 0.9,
        height: 300,
        borderRadius: 8,
        overflow: 'hidden',
    },
});
