import React, { useState } from 'react';
import { View, TouchableOpacity, FlatList, StyleSheet, Modal, Pressable, Dimensions } from 'react-native';
import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';
import { Order } from '../navigation/type';
import StatusRenderer from './StatusRenderer';
import dayjs from 'dayjs';
import RenderEmptyState from './RenderEmptyState';
import { useDispatch, useSelector } from 'react-redux';
import { closeModal, openModal, selectIsModalOpen } from '../features/modal/modalsSlice';

type Props = {
    data: string[];
    selected: string | null;
    onSelect: (item: string) => void;
    placeholder?: string;
    disabled?: boolean;
    onRefresh?: () => void;
    loading?: boolean;
    title?: string;
};

export default function SimpleDropdown({
    data,
    selected,
    onSelect,
    placeholder = "Select an item",
    disabled = false,
    onRefresh = () => { },
    loading = false,
    title = "Select an item",

}: Props) {
    const modalId = 'simple-dropdown';
    const theme = useTheme();
    const isOpen = useSelector(selectIsModalOpen(modalId));
    const dispatch = useDispatch();
    const open = () => dispatch(openModal({ id: modalId }));
    const close = () => dispatch(closeModal(modalId));

    const selectedItem = selected && data.find(item => item === selected);

    const handleSelect = (item: string) => {
        onSelect(item);
        close();
    };

    const renderOrderItem = ({ item }: { item: string }) => {
        const isSelected = selected === item;
        return (

            <TouchableOpacity
                style={[
                    styles.dropdownItem,
                    {
                        borderBottomColor: theme.text + '20',
                        backgroundColor: isSelected ? theme.primary + '10' : 'transparent'
                    }
                ]}
                onPress={() => handleSelect(item)}
            >
                <View style={{ flex: 1, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                    <ThemedText
                        variant="subtitle"
                        style={[
                            styles.orderNum,
                            { color: theme.text + '80' },
                            isSelected && { color: theme.primary }
                        ]}
                    >
                        {item} ?
                    </ThemedText>

                </View>

            </TouchableOpacity>
        )
    };

    return (
        <>
            <TouchableOpacity
                style={[
                    styles.dropdown,
                    {
                        backgroundColor: theme.inputBackground,
                        borderColor: theme.text + '30',
                        opacity: disabled ? 0.5 : 1
                    }
                ]}
                onPress={() => !disabled && open()}
                disabled={disabled}
            >
                <View style={styles.dropdownContent}>
                    {selectedItem ? (
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', gap: 12 }}>
                            <View style={{ justifyContent: 'center', alignItems: 'center' }}>

                                <ThemedText
                                    variant="body"
                                    numberOfLines={1}
                                    style={{ color: theme.primary, fontWeight: 'bold' }}
                                >{selectedItem}</ThemedText>
                            </View>

                        </View>
                    ) : (
                        <ThemedText
                            variant="body"
                            style={[styles.placeholder, { color: theme.text + '60' }]}
                        >
                            {placeholder}
                        </ThemedText>
                    )}
                </View>
                <AntDesign
                    name={isOpen ? "up" : "down"}
                    size={16}
                    color={theme.text + '60'}
                />
            </TouchableOpacity>

            <Modal
                visible={isOpen}
                transparent
                animationType="fade"
                onRequestClose={close}
            >
                <Pressable style={[{
                    justifyContent: 'center',
                    paddingHorizontal: 20, flex: 1, marginTop: 24
                }]} onPress={close} >
                    <View style={[styles.dropdownModal, { backgroundColor: theme.cardBackground }]}>
                        <View style={styles.modalHeader}>
                            <ThemedText variant="title">{title}</ThemedText>
                            <TouchableOpacity onPress={close}>
                                <AntDesign name="close" size={24} color={theme.text} />
                            </TouchableOpacity>
                        </View>

                        <FlatList
                            contentContainerStyle={[
                                data.length === 0 && { flex: 1, padding: 20 }
                            ]}
                            data={data}
                            renderItem={renderOrderItem}
                            keyExtractor={(item) => item}
                            style={styles.ordersList}
                            showsVerticalScrollIndicator={false}
                            refreshing={loading}
                            onRefresh={onRefresh}
                            ListEmptyComponent={() => <RenderEmptyState state="default" />}
                        />
                    </View>
                </Pressable>
            </Modal>
        </>
    );
}

const styles = StyleSheet.create({
    dropdown: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
        borderWidth: 1,
        minHeight: 56,
    },
    dropdownContent: {
        flex: 1,
        marginRight: 8,
    },
    selectedOrderNum: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 2,
    },
    selectedOrderDescription: {
        fontSize: 14,
    },
    placeholder: {
        fontSize: 16,
    },
    overlay: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        paddingHorizontal: 20,
    },
    dropdownModal: {
        borderRadius: 12,
        maxHeight: '70%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    ordersList: {
        maxHeight: 300,
    },
    dropdownItem: {
        padding: 16,
        borderBottomWidth: 1,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    orderNum: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    orderDescription: {
        fontSize: 14,
        lineHeight: 18,
    },
});
