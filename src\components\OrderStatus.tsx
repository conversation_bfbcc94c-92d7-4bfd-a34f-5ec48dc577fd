
import React from 'react';
import { View, StyleSheet, FlatList, Animated, Text } from 'react-native';
import useTheme from '../theme/useTheme';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';
import { capitalizeString } from '../helpers/global';


interface Props {
    orderStatus: string;
}

export default function OrderStatus({ orderStatus }: Props) {
    const theme = useTheme();
    //get status from orderslice with selector
    const { status, statusKeys } = useSelector((state: RootState) => state.orders);


    const getStatusStyle = () => {

        switch (orderStatus) {
            case statusKeys[0]:
                return { backgroundColor: '#FFD700', borderColor: "#FFD700", label: capitalizeString(status[orderStatus]) }; // gold
            case statusKeys[1]:
                return { backgroundColor: '#1E90FF', borderColor: "#1E90FF", label: capitalizeString(status[orderStatus]) }; // blue
            case statusKeys[2]:
                return { backgroundColor: '#32CD32', borderColor: "#32CD32", label: capitalizeString(status[orderStatus]) }; // green
            case statusKeys[3]:
                return { backgroundColor: '#cd4432', borderColor: "#cd4432", label: capitalizeString(status[orderStatus]) }; // green
            default:
                return { backgroundColor: '#515151', borderColor: "#ccc", label: 'Unknown' };
        }
    };

    const { backgroundColor, borderColor, label } = getStatusStyle();

    return (
        <View style={[styles.chip, { backgroundColor, borderColor }]}>
            <Text style={[styles.text, { color: 'white' }]}>{label}</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    chip: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 5,
        borderWidth: 1,
        alignSelf: 'flex-start',
    },
    text: {
        fontSize: 12,
        fontWeight: 'bold',
    },
});
