import React, { useEffect } from 'react';
import { View, TouchableOpacity, FlatList, StyleSheet } from 'react-native';
import { AntDesign, FontAwesome6 } from '@expo/vector-icons';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../app/store';
import { selectWarehouse, fetchWarehouses } from '../features/warehouses/warehouseSlice';
import { fetchCollectionsByWarehouseId } from '../features/collections/CollectionsThunks';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import { Warehouse } from '../features/warehouses/warehouseSlice';
import ThreeDotsSkeleton from './ThreeDotsSkeleton';
import RenderEmptyState from './RenderEmptyState';

export default function WarehouseDropdownDrawer() {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const { warehouses, selectedWarehouse, loading } = useSelector((state: RootState) => state.warehouses);

    const [expanded, setExpanded] = React.useState(false);
    const dropdownHeight = useSharedValue(0);




    const toggleDropdown = () => {
        setExpanded(!expanded);
        dropdownHeight.value = withTiming(!expanded ? 200 : 0, { duration: 200 });
    };

    const handleSelect = (warehouse: Warehouse) => {
        setExpanded(false);
        dropdownHeight.value = withTiming(0, { duration: 200 });
        dispatch(selectWarehouse(warehouse));
        dispatch(fetchCollectionsByWarehouseId(warehouse.id));
    };
    const onRefresh = () => {
        dispatch(fetchWarehouses());
    };

    const animatedStyle = useAnimatedStyle(() => ({
        height: dropdownHeight.value,
        overflow: 'hidden',
        borderRadius: 6,
        position: 'absolute',
        width: '100%',
        zIndex: 1,
        backgroundColor: theme.cardBackground,
    }));

    const renderItem = ({ item }: { item: Warehouse }) => {
        const isSelected = selectedWarehouse?.id === item.id;
        return (
            <TouchableOpacity
                onPress={() => handleSelect(item)}
                style={[
                    styles.item,
                    {
                        backgroundColor: isSelected ? theme.primary + '20' : theme.cardBackground,
                        borderBottomColor: theme.text + '10',
                    },
                ]}
            >
                <View style={styles.iconRow}>
                    <FontAwesome6 name="cubes-stacked" size={16} color={theme.text} />
                    <ThemedText style={[styles.itemText, { color: theme.text }]}>
                        {item.name}
                    </ThemedText>
                </View>
                {isSelected && (
                    <AntDesign name="check" size={16} color={theme.primary} />
                )}
            </TouchableOpacity>
        );
    };

    return (
        <View style={[styles.wrapper, { backgroundColor: theme.background }]}>
            <TouchableOpacity
                onPress={toggleDropdown}
                style={[styles.header, { backgroundColor: theme.cardBackground }]}
            >
                <ThemedText style={styles.selectedText}>
                    {selectedWarehouse?.name || 'Select Warehouse'}
                </ThemedText>
                <AntDesign
                    name={expanded ? 'up' : 'down'}
                    size={14}
                    color={theme.text}
                />
            </TouchableOpacity>

            <Animated.View style={[animatedStyle]}>
                {loading ? (
                    <View style={{ padding: 12 }}>
                        <ThreeDotsSkeleton />
                    </View>
                ) : (
                    <FlatList
                        data={warehouses}
                        keyExtractor={(item) => item.id.toString()}
                        renderItem={renderItem}
                        scrollEnabled={true}
                        showsVerticalScrollIndicator={false}
                        ListEmptyComponent={() => <RenderEmptyState state="Warehouse" />}
                        refreshing={loading}
                        onRefresh={onRefresh}
                    />
                )}
            </Animated.View>
        </View>
    );
}

const styles = StyleSheet.create({
    wrapper: {
        marginHorizontal: 10,
        marginTop: 10,
        borderRadius: 6,
        position: 'relative',
    },
    header: {
        paddingHorizontal: 14,
        paddingVertical: 12,
        borderRadius: 6,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    selectedText: {
        fontSize: 16,
        fontWeight: '600',
    },
    item: {
        paddingHorizontal: 14,
        paddingVertical: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
    },
    iconRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    itemText: {
        fontSize: 14,
    },
});
