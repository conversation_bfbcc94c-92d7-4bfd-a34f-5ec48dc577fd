import React from 'react';
import { Provider } from 'react-redux';
import { store } from './src/app/store';
import AppNavigator from './src/navigation/AppNavigator';
import { ThemeProvider } from './src/theme/ThemeContext';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { enableScreens } from 'react-native-screens';
import { ToastProvider } from './src/features/toast/ToastContext';
import { View } from 'react-native';
import GlobalOverlay from './src/features/modal/GlobalOverlay';

export default function App() {
  enableScreens();
  return (
    <GestureHandlerRootView style={{ flex: 1, position: 'relative' }}>

      <Provider store={store}>
        <ThemeProvider>
          <ToastProvider>
            <GlobalOverlay />
            <AppNavigator />
          </ToastProvider>
        </ThemeProvider>
      </Provider>
    </GestureHandlerRootView>

  );
}
