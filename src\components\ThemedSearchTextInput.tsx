import React, { useState, forwardRef } from 'react';
import {
    TextInput,
    StyleSheet,
    TextInputProps,
    View,
    TouchableOpacity,
} from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedIcon from './ThemedIcon';

interface ThemedTextInputProps extends TextInputProps {
    bgColor?: string;
    endComponent?: React.ReactNode;
}

const ThemedSearchTextInput = forwardRef<TextInput, ThemedTextInputProps>(
    ({ style, ...props }, ref) => {
        const theme = useTheme();

        return (
            <View style={[styles.container, { backgroundColor: props.bgColor || theme.inputBackground }]}>
                <TextInput
                    ref={ref}
                    placeholderTextColor={theme.text}
                    style={[
                        styles.input,
                        { color: theme.text },
                        style,
                    ]}
                    {...props}
                />
                {props.endComponent}

            </View>
        );
    }
);

ThemedSearchTextInput.displayName = 'ThemedSearchTextInput';

export default ThemedSearchTextInput;

const styles = StyleSheet.create({
    container: {
        width: '100%',
        borderRadius: 8,
        position: 'relative',
        justifyContent: 'center',
        flexDirection: 'row',
        alignItems: 'center',
    },
    input: {
        padding: 12,
        fontSize: 16,
    },
    icon: {
        position: 'absolute',
        right: 12,
        top: '50%',
        marginTop: -10,
    },
});
