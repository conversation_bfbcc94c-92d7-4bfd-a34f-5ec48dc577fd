// src/features/collections/collectionSlice.ts
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchCollectionById, fetchCollectionsByWarehouseId, getRecipients, setConfirmationDelivery, updateCollectionStatus } from './CollectionsThunks';
import axiosClient from '../../api/axiosClient';
import { Collection, DashboardFilter, Recipient } from '../../navigation/type';



const initialState: CollectionsState = {
    collections: [],
    filter: {
        startDate: '',
        endDate: '',
        status: '',
        keyword: ''
    },
    recipients: [],
    loading: false,
    error: null,
    collectionStatus: ['All', 'created', 'processing', 'intransit']

};

interface CollectionsState {
    collections: Collection[];
    filter: DashboardFilter;
    recipients: Recipient[];
    loading: boolean;
    error: string | null;

    collectionStatus: string[]
}




const collectionSlice = createSlice({
    name: 'collections',
    initialState,
    reducers: {
        replaceCollection(state, action: PayloadAction<Collection>) {
            const index = state.collections.findIndex(col => col.id === action.payload.id);
            if (index !== -1) {
                state.collections[index] = action.payload;
            }
        },
        setFilter: (state, action: PayloadAction<Partial<DashboardFilter>>) => {
            state.filter = { ...state.filter, ...action.payload };
        },
        clearFilter: (state) => {
            state.filter = initialState.filter;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchCollectionsByWarehouseId.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchCollectionsByWarehouseId.fulfilled, (state, action) => {
                state.loading = false;
                state.collections = action.payload;
            })
            .addCase(fetchCollectionsByWarehouseId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(fetchCollectionById.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(fetchCollectionById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(fetchCollectionById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateCollectionStatus.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(updateCollectionStatus.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(updateCollectionStatus.pending, (state) => {
                state.loading = true;
                state.error = null;
            }).addCase(setConfirmationDelivery.fulfilled, (state, action) => {
                state.loading = false;
                const [collectionId, confirmation] = action.payload;
                // update the collection confirmation
                const index = state.collections.findIndex(col => col.id === collectionId);
                if (index !== -1) {
                    state.collections[index].confirmation = confirmation;
                }
            })
            .addCase(setConfirmationDelivery.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(setConfirmationDelivery.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getRecipients.fulfilled, (state, action) => {
                state.recipients = action.payload;
            })
            .addCase(getRecipients.rejected, (state, action) => {
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(getRecipients.pending, (state) => {
                state.error = null;
                state.recipients = [];
            })


            ;
    },
});

export const { replaceCollection, setFilter, clearFilter } = collectionSlice.actions; // Export the action creator

export default collectionSlice.reducer;
