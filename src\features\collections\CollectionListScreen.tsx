import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View, FlatList, Dimensions, StyleSheet, ActivityIndicator, TouchableOpacity, ScrollView, RefreshControl, Modal } from 'react-native';
import ThemedText from '../../components/ThemedText';
import CollectionCard from '../../components/CollectionCard';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Pressable, Swipeable } from 'react-native-gesture-handler';
import { LinearGradient } from 'expo-linear-gradient';
import { AntDesign, Entypo, Feather, FontAwesome, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../app/store';
import { fetchCollectionProducts, fetchCollectionsByWarehouseId } from './CollectionsThunks';
import PageTitleSkeleton from '../../components/PageTitleSkeleton';
import { Collection, CollectionStackParamList, OrderProduct } from '../../navigation/type';
import ThreeDotsSkeleton from '../../components/ThreeDotsSkeleton';
import CollectionCardSkeleton from '../../components/CollectionCardSkeleton';
import ThemedView from '../../components/ThemedView';
import RenderEmptyState from '../../components/RenderEmptyState';
import { generateCollectionDetailsPDF } from '../../utils/pdfGenerator';
import GlobalLayout from '../../components/GlobalLayout';
import Animated, { FadeInDown, runOnJS, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { setFilter } from './collectionSlice';
import useTheme from '../../theme/useTheme';
import ThemedTextInput from '../../components/ThemedTextInput';
import SimpleDropdown from '../../components/SimpleDropdown';
import StatusRenderer from '../../components/StatusRenderer';
import { Modalize } from 'react-native-modalize';

export default function CollectionListScreen() {
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<NativeStackNavigationProp<CollectionStackParamList>>();
    const { collections, loading, filter, collectionStatus } = useSelector((state: RootState) => state.collections);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const theme = useTheme();
    const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null)
    const [Tab, setTab] = useState(3)
    const [currentTab, setCurrentTab] = useState(0)
    const scrollY = useSharedValue(0);
    const stickyHeaderOpacity = useSharedValue(0);
    const stickyHeaderTranslateY = useSharedValue(-60);
    const [showFilter, setShowFilter] = useState(false)
    const [isOpen, setIsOpen] = useState(false)
    const [search, setSearch] = useState<string>(filter.keyword ?? '');
    const debounceRef = useRef<NodeJS.Timeout | null>(null);
    const scrollViewRef = useRef<ScrollView>(null);

    const modalRef = useRef<Modalize>(null);

    useEffect(() => {
        // skip dispatch if the keyword is already the same
        if ((filter.keyword ?? '') === (search ?? '')) return;

        if (debounceRef.current) clearTimeout(debounceRef.current);

        debounceRef.current = setTimeout(() => {
            // normalize to empty string when cleared
            const keyword = (search || '').trim();
            dispatch(setFilter({ ...filter, keyword }));
        }, 400); // tweak delay as you like

        return () => {
            if (debounceRef.current) clearTimeout(debounceRef.current);
        };
        // only depend on `search` so tab/date changes don't re-fire this
    }, [search]);

    const handleSearch = () => {
        const keyword = (search || '').trim();
        if ((filter.keyword ?? '') !== keyword) {
            dispatch(setFilter({ ...filter, keyword }));
        }
    };

    useEffect(() => {
        if (showFilter) {
            // Scroll to top when filter appears
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    }, [showFilter]);
    //get collection added today
    useEffect(() => {
        dispatch(fetchCollectionsByWarehouseId(selectedWarehouse?.id))

    }, [filter, selectedWarehouse?.id])
    //set filter end date start date based on the tab
    useEffect(() => {

        if (Tab === 0) {
            dispatch(setFilter({ startDate: new Date().toISOString(), endDate: new Date().toISOString() }));
        }
        if (Tab === 1) {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);
            dispatch(setFilter({ startDate: startDate.toISOString(), endDate: new Date().toISOString() }));
        }
        if (Tab === 2) {
            const now = new Date();
            const startOfMonthUtc = new Date(Date.UTC(
                now.getUTCFullYear(),
                now.getUTCMonth(),
                1, 0, 0, 0, 0
            ));

            dispatch(setFilter({ startDate: startOfMonthUtc.toISOString(), endDate: now.toISOString() }));
        }
        if (Tab === 3) {
            dispatch(setFilter({ startDate: '', endDate: '' }));
        }
    }, [Tab, dispatch, currentTab])

    const stickyHeaderAnimatedStyle = useAnimatedStyle(() => {
        return {
            opacity: stickyHeaderOpacity.value,
            transform: [{ translateY: stickyHeaderTranslateY.value }],
        };
    });
    const updateStickyHeader = (shouldShow: boolean) => {
        'worklet';
        stickyHeaderOpacity.value = withTiming(shouldShow ? 1 : 0, { duration: 200 });
        stickyHeaderTranslateY.value = withTiming(shouldShow ? 0 : -60, { duration: 200 });
    };
    const handleScroll = (event: any) => {
        const currentScrollY = event.nativeEvent.contentOffset.y;
        scrollY.value = currentScrollY;
        const threshold = 100;
        const shouldShow = currentScrollY >= threshold;

        runOnJS(updateStickyHeader)(shouldShow);
    };

    useEffect(() => {
        if (selectedWarehouse) {
            dispatch(fetchCollectionsByWarehouseId(selectedWarehouse.id));
        }
    }, [dispatch, selectedWarehouse?.id]);

    const onRefresh = useCallback(() => {
        if (selectedWarehouse) {
            dispatch(fetchCollectionsByWarehouseId(selectedWarehouse.id));
        }
    }, [dispatch, selectedWarehouse]);

    const handleTabChange = (tab: number) => {
        setTab(tab);
    };

    const showCollectOption = (collection: Collection) => {
        setSelectedCollection(collection);

        modalRef.current?.open();
    };
    function SwipeableCollectionRow({ item, onPress }: { item: any; onPress: () => void }) {
        const swipeRef = useRef<Swipeable>(null);
        const [loadingData, setLoadingData] = useState(false)


        const printCollection = async () => {
            setLoadingData(true)
            const res = await dispatch(fetchCollectionProducts(item.id));
            if (fetchCollectionProducts.fulfilled.match(res)) {
                // cast as product 
                const allProducts = res.payload as OrderProduct[];
                const groupedProducts: { [sku: string]: { name: string; totalQty: number; items: { product: OrderProduct; orderNum: string }[] } } = {};

                allProducts.forEach(({ sku, name, quantity, ...rest }) => {
                    if (!groupedProducts[sku]) {
                        groupedProducts[sku] = { name, totalQty: 0, items: [] };
                    }
                    groupedProducts[sku].totalQty += quantity;
                    groupedProducts[sku].items.push({ product: { sku, name, quantity, ...rest }, orderNum: rest.orderNum });
                });

                generateCollectionDetailsPDF(item, groupedProducts);

            }
            setLoadingData(false)
            swipeRef.current?.close();
        };



        const screenWidth = Dimensions.get('window').width;

        const renderRightActions = (collection: any, dragX: any) => {

            return (
                <LinearGradient
                    colors={['transparent', '#007AFF']}
                    start={{ x: 0, y: 0.5 }}
                    end={{ x: 1, y: 0.5 }}
                    style={{
                        width: screenWidth,
                        maxWidth: 80,
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        paddingRight: 20,
                        borderRadius: 8,
                        marginVertical: 2,
                    }}
                >
                    {loadingData ? <ActivityIndicator size="small" color="#fff" /> : <AntDesign name="printer" size={24} color="#fff" />}
                </LinearGradient>
            );
        };

        return (
            <Swipeable
                ref={swipeRef}
                renderRightActions={(progress, dragX) => selectedCollection?.id === item.id ? null : renderRightActions(item, dragX)}
                onSwipeableOpen={printCollection}
                containerStyle={{ marginBottom: 10 }}
            >
                <CollectionCard selected={selectedCollection?.id === item.id} onOptionClick={(item) => showCollectOption(item)} collection={item} onPress={onPress} />
            </Swipeable>
        );
    }

    const renderStatusItem = ({ item }: { item: string }) => {
        const isSelected = filter.status === item;
        return (

            <TouchableOpacity
                style={
                    {
                        borderBottomWidth: 1,
                        paddingHorizontal: 10,
                        borderBottomColor: theme.text + '20',
                        backgroundColor: isSelected ? theme.primary + '10' : 'transparent'
                    }
                }
                onPress={() => { setIsOpen(false); dispatch(setFilter({ ...filter, status: item })); }}
            >
                <View style={{ padding: 10, flex: 1, justifyContent: 'flex-start', alignItems: 'flex-start' }}>

                    {item === 'All' ? <ThemedText variant="subtitle" style={{ color: theme.text, paddingVertical: 5, paddingHorizontal: 10 }}>All</ThemedText> : <StatusRenderer status={item} />
                    }
                </View>

            </TouchableOpacity >
        )
    };




    return (
        <GlobalLayout header={<View style={{ gap: 10, display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
            <ThemedText variant="title" style={{ color: theme.text, marginLeft: 10 }}>
                List of Collects
            </ThemedText>
            <TouchableOpacity onPress={() => setShowFilter(!showFilter)} style={{ height: 34, width: 34, padding: 5, display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center', backgroundColor: theme.primary, borderRadius: 10 }}>
                {showFilter ? <Feather name="x" size={20} color={'white'} /> : <Feather name="filter" size={20} color={'white'} />}
            </TouchableOpacity>

        </View>}>
            {/* flatten button */}

            <Animated.View style={[
                {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    zIndex: 1000,
                    backgroundColor: theme.background,
                    borderBottomWidth: 1,
                    borderBottomColor: theme.text + '20',
                    paddingHorizontal: 20,
                    paddingVertical: 8,
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    gap: 12,

                },
                stickyHeaderAnimatedStyle
            ]}  >


                <View style={{
                    display: 'flex',
                    backgroundColor: theme.cardBackground,
                    borderRadius: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingHorizontal: 5,
                    paddingVertical: 5,
                    width: '100%',
                }}>
                    {/* All */}
                    <TouchableOpacity onPress={() => handleTabChange(3)} style={{ position: 'relative', backgroundColor: Tab === 3 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            All
                        </ThemedText>
                        {Tab === 3 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleTabChange(0)} style={{ position: 'relative', backgroundColor: Tab === 0 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            Today
                        </ThemedText>
                        {Tab === 0 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleTabChange(1)} style={{ position: 'relative', backgroundColor: Tab === 1 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            This Week
                        </ThemedText>
                        {Tab === 1 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleTabChange(2)} style={{ position: 'relative', backgroundColor: Tab === 2 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            This Month
                        </ThemedText>
                        {Tab === 2 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                </View>
            </Animated.View>

            <ScrollView ref={scrollViewRef} onScroll={handleScroll} refreshControl={
                <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor={theme.primary} />
            } style={{ flex: 1, paddingHorizontal: 15, paddingVertical: 20, gap: 6 }}>

                {showFilter && <View style={{
                    display: 'flex',
                    borderRadius: 10,
                    marginBottom: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    overflow: 'hidden',
                    gap: 10,
                }}>
                    <ThemedTextInput
                        style={{ flex: 1, backgroundColor: theme.cardBackground, borderRadius: 10, width: '100%' }}
                        placeholder="Search"
                        value={search}
                        onChangeText={setSearch}
                        keyboardType="default"
                        autoCapitalize="none"
                        returnKeyType="go"
                        onSubmitEditing={handleSearch}
                    />

                    <TouchableOpacity onPress={() => setIsOpen(true)}
                        style={{
                            margin: 0,
                            flex: 0.3,
                            padding: 5, display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'flex-start',
                            alignItems: 'flex-start',
                            backgroundColor: theme.cardBackground, borderRadius: 10
                        }}>
                        <View style={{
                            width: '100%',
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'flex-start',
                        }}><ThemedText variant="body" >
                                Status
                            </ThemedText>
                            <Feather name="chevron-down" size={20} color={theme.text} />
                        </View>

                        <ThemedText variant="body" style={{ fontWeight: 'bold' }}>
                            {filter.status || 'All'}
                        </ThemedText>

                    </TouchableOpacity>
                    <Modal
                        visible={isOpen}
                        transparent
                        animationType="fade"
                        onRequestClose={() => setIsOpen(false)}
                    >
                        <Pressable style={[styles.overlay, { flex: 1, marginTop: 24 }]} onPress={() => setIsOpen(false)}>
                            <View style={[styles.dropdownModal, { backgroundColor: theme.cardBackground }]}>
                                <View style={styles.modalHeader}>
                                    <ThemedText variant="title">Statut</ThemedText>
                                    <TouchableOpacity onPress={() => setIsOpen(false)}>
                                        <AntDesign name="close" size={24} color={theme.text} />
                                    </TouchableOpacity>
                                </View>

                                <FlatList

                                    data={collectionStatus}
                                    renderItem={renderStatusItem}
                                    keyExtractor={(item) => item}
                                    style={styles.ordersList}
                                    showsVerticalScrollIndicator={false}
                                    ListEmptyComponent={() => <RenderEmptyState state="default" />}
                                />
                            </View>
                        </Pressable>
                    </Modal>
                </View>}
                <View style={{
                    display: 'flex',
                    backgroundColor: theme.cardBackground,
                    borderRadius: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingHorizontal: 5,
                    paddingVertical: 5,
                    marginBottom: 20,
                }}>
                    {/* All */}
                    <TouchableOpacity onPress={() => handleTabChange(3)} style={{ backgroundColor: Tab === 3 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            All
                        </ThemedText>
                        {Tab === 3 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleTabChange(0)}
                        style={{ position: 'relative', backgroundColor: Tab === 0 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            Today
                        </ThemedText>
                        {Tab === 0 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleTabChange(1)} style={{ position: 'relative', backgroundColor: Tab === 1 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            This Week
                        </ThemedText>
                        {Tab === 1 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleTabChange(2)} style={{ position: 'relative', backgroundColor: Tab === 2 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                        <ThemedText variant="body" style={{ textAlign: 'center' }}>
                            This Month
                        </ThemedText>
                        {Tab === 2 && <View
                            style={{
                                position: 'absolute',
                                width: 30, bottom: 0, backgroundColor: theme.primary,
                                borderRadius: 20, height: 5, left: '50%',
                                transform: [{ translateX: -5 }],
                            }} />}
                    </TouchableOpacity>
                </View>
                {collections.length === 0 ? <View style={styles.loadingContainer}>
                    <RenderEmptyState subtitle={` No collections added this period. `} state="Collection" />
                </View> : collections.map((item, idx) => (
                    <SwipeableCollectionRow
                        key={idx}
                        item={item}
                        onPress={() => navigation.navigate('CollectionDetails', { collectionId: item.id })} />
                ))}
            </ScrollView>
            <Modalize
                ref={modalRef}
                modalHeight={Dimensions.get('window').height * 0.4}
                onClose={() => setSelectedCollection(null)}
                modalStyle={[styles.modal, { backgroundColor: theme.cardBackground }]}
                handleStyle={{ backgroundColor: theme.primary }}
                withHandle
            >
                <View style={styles.modalContent}>
                    {/* Header */}
                    <View style={styles.headerModal}>
                        <ThemedText variant="title" style={styles.modalTitle}>
                            No.{selectedCollection?.num}
                        </ThemedText>
                    </View>
                    <Animated.View entering={FadeInDown.delay(0).duration(200)} style={{
                        width: '100%',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: 5,
                        marginTop: 10,
                    }}>
                        <View style={{ flex: 1, gap: 5 }}>
                            <Animated.View entering={FadeInDown.delay(50).duration(300)}>
                                <TouchableOpacity style={styles.option}>
                                    <MaterialCommunityIcons name="file-excel" size={24} color={theme.text} />
                                    <ThemedText variant="body">Export</ThemedText>
                                </TouchableOpacity>
                            </Animated.View>

                            <Animated.View entering={FadeInDown.delay(100).duration(300)}>
                                <TouchableOpacity style={styles.option}>
                                    <Entypo name="print" size={24} color={theme.text} />
                                    <ThemedText variant="body">Print</ThemedText>
                                </TouchableOpacity>
                            </Animated.View>
                        </View>

                        <View style={{ flex: 1, gap: 10 }}>
                            <Animated.View entering={FadeInDown.delay(150).duration(300)}>
                                <TouchableOpacity style={styles.option}>
                                    <MaterialIcons name="summarize" size={24} color={theme.text} />
                                    <ThemedText variant="body">Print Summary</ThemedText>
                                </TouchableOpacity>
                            </Animated.View>

                            <Animated.View entering={FadeInDown.delay(200).duration(300)}>
                                <TouchableOpacity style={styles.option}>
                                    <FontAwesome name="print" size={24} color={theme.text} />
                                    <ThemedText variant="body">Print Labels</ThemedText>
                                </TouchableOpacity>
                            </Animated.View>
                        </View>
                    </Animated.View>
                </View>

            </Modalize>
        </GlobalLayout>
    );
}

const styles = StyleSheet.create({
    modal: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    modalContent: {
        padding: 20,
    },
    modalTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    headerSection: {
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        paddingVertical: 16,
    },
    option: {
        height: 40,
        display: 'flex',
        flexDirection: 'row',
        gap: 10,
        alignItems: 'center',
    },
    headerModal: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
    },
    listContainer: {
        paddingHorizontal: 20,
    },
    emptyListContainer: {
        flex: 1,
    },
    overlay: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        paddingHorizontal: 20,
    },
    ordersList: {
        maxHeight: 300,
    },
    dropdownModal: {
        borderRadius: 12,
        maxHeight: '70%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 16,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
});

