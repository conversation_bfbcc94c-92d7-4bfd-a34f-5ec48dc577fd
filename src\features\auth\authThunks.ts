// features/auth/authThunks.ts
import { createAsyncThunk } from '@reduxjs/toolkit';
import { loginSuccess, logout, setUser } from './authSlice';
import axiosClient from '../../api/axiosClient';
import { RootState } from '../../app/store';

export const loginUser = createAsyncThunk(
    'auth/loginUser',
    async (
        credentials: { email: string; password: string },
        { dispatch, rejectWithValue }
    ) => {
        try {
            const response = await axiosClient.post('/login', credentials);
            const { token, user } = response.data;

            // Save token to localStorage or AsyncStorage if needed
            dispatch(loginSuccess({ token, user }));

            return { token, user };
        } catch (err: any) {
            return rejectWithValue(
                err.response?.data?.message || 'Login failed'
            );
        }
    }
);

export const logoutUser = createAsyncThunk(
    'auth/logoutUser',
    async (_, { dispatch, rejectWithValue }) => {
        try {
            await axiosClient.post('/user/login', {});
            dispatch(logout());
        } catch (err: any) {
            return rejectWithValue(

                err.response?.data?.message || 'Logout failed'
            );
        }
    }
);

export const SetSignature = createAsyncThunk(
    'auth/setSignature',
    async (signature: string, { dispatch, rejectWithValue }) => {
        try {

            const response = await axiosClient.post('/user/setSignature', { signature });
            dispatch(setUser({ signature }));
            return response.data;
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error setting signature');
        }
    }
);
