export type OrderProduct = {
    id: number;
    name: string;
    quantity: number;
    sku: string;
    status: string;
    orderNum: string;
};


export type Confirmation = {
    id: number;
    deliverySignedAt: string | null;
    userSignature: string | null;
    recipientId: number;
    recipient: Recipient | null;
}
export type Collection = {
    id: number;
    num: string;
    location: string;
    status: string;
    shippingType: string;
    shippingBy: string;
    shippedAt: string;
    orders: number;
    addedAt: string;
    useBulk: boolean;
    confirmation: Confirmation | null;
}

export type Recipient = {
    id: number;
    fullName: string;
    signature: string | null;
}
export type Order = {
    id: number;
    orderNum: string;
    orderCode: string;
    status: string;
    goodsDescription: string;
    ordersProducts: OrderProduct[];
    shippedAt: string;
    updatedAt: string;
    [key: string]: any; // allow extra fields
};

export type DashboardFilter = {
    startDate: string;
    endDate: string;
    status: string;
    keyword: string;
}

export type Return = {
    id: number;
    ref: string;
    orders: Order[];
    returnReason: string;
    returnComment: string;
    images: string[]; // comma-separated URLs
    createdAt: string;
};

export type ReturnReason = {
    Label: string;
    requireComment: boolean;
    requireImage: boolean;
};

export type RootStackParamList = {
    Splash: undefined;
    Login: undefined;
    MainApp: undefined;
};

export type DrawerParamList = {
    Dashboard: undefined;
    Collections: undefined;
    Returns: undefined;
    CollectionDetails: { collectionId: number };
    Fulfillment: { collectionId: number; orders: Order[], action: string };
    Acknowledgment: { collectionId: number; orders: Order[] };

};

export type MainTabParamList = {
    Collections: undefined;
    Returns: undefined;
};

export type CollectionStackParamList = {
    MainDrawer: undefined;
    CollectionDetails: { collectionId: number };
    Fulfillment: { collectionId: number; orders: Order[], action: string };
    Acknowledgment: { collectionId: number; orders: Order[] };
};

export type ReturnsStackParamList = {
    ReturnsList: undefined;
    AddReturn: undefined;
    EditReturn: { returnId: number };
};