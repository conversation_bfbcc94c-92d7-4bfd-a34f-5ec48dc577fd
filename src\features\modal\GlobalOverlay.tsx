// GlobalOverlay.tsx
import React from 'react';
import { Pressable, View, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { closeTopModal, selectTopModalId } from '../modal/modalsSlice';

export default function GlobalOverlay() {
    const dispatch = useDispatch();
    const topId = useSelector(selectTopModalId);
    const isAnyOpen = !!topId;

    if (!isAnyOpen) return null;

    return (
        <Pressable
            style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                zIndex: 9999,
            }}
            onPress={() => {
                console.log('closeTopModal');
                dispatch(closeTopModal())
            }}
        >
        </Pressable>
    );
}

const styles = StyleSheet.create({
    backdrop: {
        flex: 1,

    },
});
