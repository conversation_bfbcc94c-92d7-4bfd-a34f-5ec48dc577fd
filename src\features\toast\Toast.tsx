// toast/Toast.tsx
import React, { useRef, useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    Animated,
    TouchableOpacity,
} from 'react-native';
import useTheme from '../../theme/useTheme';

export default function Toast({
    id,
    message,
    type = 'success',
    onClose,
    index = 0,
}: {
    id: string;
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    onClose: () => void;
    index: number;
}) {
    const theme = useTheme();
    const opacity = useRef(new Animated.Value(1)).current;
    const translateY = useRef(new Animated.Value(50)).current;
    const [closed, setClosed] = useState(false);

    useEffect(() => {
        console.log(`Toast mounted: ${message}`);
    }, []);

    useEffect(() => {
        Animated.timing(translateY, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
        }).start();

        const timeout = setTimeout(() => {
            handleClose();
        }, 5000);

        return () => clearTimeout(timeout);
    }, []);

    const handleClose = () => {
        if (closed) return;
        setClosed(true);

        Animated.parallel([
            Animated.timing(opacity, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.timing(translateY, {
                toValue: -20,
                duration: 300,
                useNativeDriver: true,
            }),
        ]).start(() => {
            requestAnimationFrame(() => {
                onClose();
            });
        });
    };

    const backgroundColor =
        type === 'success'
            ? theme.success
            : type === 'error'
                ? theme.error
                : type === 'info'
                    ? theme.primary
                    : theme.warning;

    return (
        <Animated.View
            style={[
                styles.container,
                {
                    backgroundColor,
                    opacity,
                    transform: [{ translateY }],
                    bottom: 60 + index * 70, // stack offset
                    // top: 100 + index * 70,
                },
            ]}
        >
            <View style={styles.row}>
                <Text style={styles.text}>{message}</Text>
                <TouchableOpacity onPress={handleClose}>
                    <Text style={styles.closeText}>Close</Text>
                </TouchableOpacity>
            </View>
        </Animated.View>
    );
}

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        left: 20,
        right: 20,
        padding: 12,
        borderRadius: 10,
        zIndex: 1000,
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    text: {
        color: '#fff',
        fontSize: 14,
        flex: 1,
        marginRight: 10,
    },
    closeText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '600',
        textDecorationLine: 'underline',
    },
});
