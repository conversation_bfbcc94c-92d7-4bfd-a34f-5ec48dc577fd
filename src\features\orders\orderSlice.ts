
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';
import { DashboardFilter, Order } from '../../navigation/type';
import { RootState } from '../../app/store';


// Define the shape of the slice state
interface OrderState {
    orders: Order[];
    loading: boolean;
    filter: DashboardFilter;
    loadingStatus: boolean;
    error: string | null;
    status: Record<string, string>;
    statusKeys: string[];
}

// Initial state
const initialState: OrderState = {
    orders: [],
    loading: false,
    filter: {
        startDate: '',
        endDate: '',
        status: '',
        keyword: ''
    },
    loadingStatus: false,
    error: null,
    status: {},
    statusKeys: []
};

export const toggleProductStatus = createAsyncThunk(
    'orders/toggleProductStatus',
    async ({ productId }: { productId: number }, { rejectWithValue }) => {
        try {

            const response = await axiosClient.put(`/products/${productId}/gather`);


            return response.data.result;
        } catch (error: any) {

            return rejectWithValue(error.response?.data?.message || 'Failed to toggle product status');
        }
    }
);

export const getStatus = createAsyncThunk(
    'orders/getStatus',
    async (_, { getState, rejectWithValue }) => {
        try {
            //get the status state

            const state = getState() as RootState;
            const { status } = state.orders;
            if (Object.keys(status).length > 0) {
                return status;
            }
            const response = await axiosClient.get(`/global/status`);
            return response.data.result;
        }
        catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to get order status');
        }
    }
);

// Async thunk to fetch orders
export const fetchOrders = createAsyncThunk(
    'orders/fetchOrders',
    async ({ collectionId = null, warehouseId = null }: { collectionId: number | null, warehouseId: number | null }, { getState, rejectWithValue }) => {
        try {
            const state = getState() as RootState;
            const { filter } = state.orders;

            let query = ``;
            if (collectionId) {
                query += `collectionId=${collectionId}`;
            } else if (warehouseId) {
                query += `warehouseId=${warehouseId}`;
            }

            if (filter.startDate) {
                query += `&startDate=${filter.startDate}`;
            }
            if (filter.endDate) {
                query += `&endDate=${filter.endDate}`;
            }
            if (filter.status) {
                query += `&status=${filter.status}`;
            }
            if (filter.keyword) {
                query += `&keyword=${filter.keyword}`;
            }
            const response = await axiosClient.get(`/orders?${query}`);

            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message);
            }


            return response.data.result; // should be an array of orders
        } catch (error: any) {

            return rejectWithValue(error.response?.data?.message || 'Failed to fetch orders');
        }
    }
);
// Async thunk to fetch orders
export const AllOrders = createAsyncThunk(
    'orders/fetchAllOrders',
    async (warehouseId: number, { rejectWithValue }) => {
        try {

            const response = await axiosClient.get(`/orders?warehouseId=${warehouseId}`);

            if (response.data.response !== 'success') {
                return rejectWithValue(response.data.message);
            }

            return response.data.result; // should be an array of orders
        } catch (error: any) {

            return rejectWithValue(error.response?.data?.message || 'Failed to fetch orders');
        }
    }
);

//call change tatus
export const changeOrderStatus = createAsyncThunk(
    'orders/changeOrderStatus',
    async ({ orderId, type = 1 }: { orderId: number, type: number }, { rejectWithValue }) => {
        try {
            const status = type === 1 ? 'fullfilled' : 'shipped';
            const response = await axiosClient.put(`/orders/${orderId}/${status}`);

            //return the order
            return [orderId, response.data.result];
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to change order status');
        }
    }
);


// Slice
const orderSlice = createSlice({
    name: 'orders',
    initialState,

    reducers: {
        setFilter: (state, action: PayloadAction<Partial<DashboardFilter>>) => {
            state.filter = { ...state.filter, ...action.payload };
        },
        clearFilter: (state) => {
            state.filter = initialState.filter;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchOrders.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchOrders.fulfilled, (state, action: PayloadAction<Order[]>) => {
                state.loading = false;
                state.orders = action.payload;
            })
            .addCase(fetchOrders.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(toggleProductStatus.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(toggleProductStatus.fulfilled, (state, action) => {
                state.loading = false;

                //change the order in the state
                state.orders = state.orders.map(order => ({
                    ...order,
                    ordersProducts: order.ordersProducts.map(product =>
                        product.id === action.payload.id
                            ? { ...product, status: action.payload }
                            : product
                    )
                }))
            })
            .addCase(toggleProductStatus.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(changeOrderStatus.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(changeOrderStatus.fulfilled, (state, action) => {

                state.loading = false;
                //change the order in the state
                state.orders = state.orders.map(order => ({
                    ...order,
                    status: order.id === action.payload[0] ? action.payload[1] : order.status,
                }))
            })
            .addCase(changeOrderStatus.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(AllOrders.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(AllOrders.fulfilled, (state, action) => {
                state.loading = false;
                state.orders = action.payload;
            })
            .addCase(AllOrders.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(getStatus.pending, (state) => {
                state.loadingStatus = true;
                state.error = null;
            })
            .addCase(getStatus.fulfilled, (state, action) => {
                state.loadingStatus = false;
                state.status = action.payload;
                state.statusKeys = Object.keys(action.payload);
            })
            .addCase(getStatus.rejected, (state, action) => {
                state.loadingStatus = false;
                state.error = action.payload as string ?? 'An error occurred';
                state.status = {};
            })
            ;
    },
});

// Export the reducer
export const { setFilter, clearFilter } = orderSlice.actions; // Export the action creator
export default orderSlice.reducer;



