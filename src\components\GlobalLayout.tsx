import React, { useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { AntDesign, Feather, MaterialIcons } from '@expo/vector-icons';
import { DrawerActions } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import WarehouseDropdown from './WarehouseDropdown';
import useTheme from '../theme/useTheme';
import { useThemeMode } from '../theme/ThemeContext';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../app/store';
import ThemedText from './ThemedText';
import { getStatus } from '../features/orders/orderSlice';

interface CollectionsLayoutProps {
    children: React.ReactNode;
    title?: string;
    isInner?: boolean
    header?: React.ReactNode;
}

export default function GlobalLayout({ children, title, isInner, header }: CollectionsLayoutProps) {
    const theme = useTheme();
    const { resolvedTheme } = useThemeMode();
    const { loadingStatus } = useSelector((state: RootState) => state.orders);
    const navigation = useNavigation();
    const dispatch = useDispatch<AppDispatch>();

    useEffect(() => {
        dispatch(getStatus())
    }, [])

    const openDrawer = () => {

        // navigation.dispatch(DrawerActions.openDrawer());
        const drawer = navigation.getParent('MainDrawer' as any);
        drawer?.dispatch(DrawerActions.openDrawer());
    };

    return (
        <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
            <StatusBar style={resolvedTheme === 'dark' ? 'light' : 'dark'} />

            {/* Header */}
            <View style={[styles.header, { backgroundColor: theme.background, borderBottomColor: theme.text + '20' }]}>
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', gap: 10 }}>
                    {isInner ? <TouchableOpacity onPress={() => navigation.goBack()} style={styles.menuButton}>
                        <AntDesign name="arrowleft" size={30} color={theme.text} />
                    </TouchableOpacity> : <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
                        <Feather name="sidebar" size={24} color={theme.text} />
                    </TouchableOpacity>}
                    {loadingStatus && <ActivityIndicator />}
                </View>


                {title && <ThemedText variant="title" style={{ color: theme.text, marginLeft: 10 }}>{title}</ThemedText>}
                {header}
            </View>

            {/* Content */}
            <View style={[styles.content, { backgroundColor: theme.background }]}>
                {children}
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
    },
    header: {
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        borderBottomWidth: 1,
        position: 'relative',
        zIndex: 9999
    },
    menuButton: {
        padding: 5,
    },
    content: {
        flex: 1,
    },
});
