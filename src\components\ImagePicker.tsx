import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Alert, Image, ScrollView, Modal, Pressable } from 'react-native';
import { AntDesign, MaterialIcons } from '@expo/vector-icons';
import * as ImagePickerExpo from 'expo-image-picker';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';

type Props = {
    images: string[];
    onImagesChange: (images: string[]) => void;
    maxImages?: number;
};

export default function ImagePicker({ images, onImagesChange, maxImages = 5 }: Props) {
    const theme = useTheme();
    const [showOptions, setShowOptions] = useState(false);

    const requestPermissions = async () => {
        const { status: cameraStatus } = await ImagePickerExpo.requestCameraPermissionsAsync();
        const { status: mediaStatus } = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();

        if (cameraStatus !== 'granted' || mediaStatus !== 'granted') {
            Alert.alert(
                'Permissions Required',
                'Camera and photo library permissions are required to add images.',
                [{ text: 'OK' }]
            );
            return false;
        }
        return true;
    };

    const pickImageFromCamera = async () => {
        setShowOptions(false);

        const hasPermissions = await requestPermissions();
        if (!hasPermissions) return;

        try {
            const result = await ImagePickerExpo.launchCameraAsync({
                mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
            });

            if (!result.canceled && result.assets[0]) {
                const newImages = [...images, result.assets[0].uri];
                onImagesChange(newImages);
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to take photo');
        }
    };

    const pickImageFromGallery = async () => {
        setShowOptions(false);

        const hasPermissions = await requestPermissions();
        if (!hasPermissions) return;

        try {
            const result = await ImagePickerExpo.launchImageLibraryAsync({
                mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
                allowsMultipleSelection: true,
            });

            if (!result.canceled && result.assets.length > 0) {
                const newImageUris = result.assets.map(asset => asset.uri);
                const allImages = [...images, ...newImageUris];

                // Limit to maxImages
                const limitedImages = allImages.slice(0, maxImages);
                onImagesChange(limitedImages);

                if (allImages.length > maxImages) {
                    Alert.alert(
                        'Image Limit',
                        `Maximum ${maxImages} images allowed. Some images were not added.`
                    );
                }
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to pick images');
        }
    };

    const removeImage = (index: number) => {
        const newImages = images.filter((_, i) => i !== index);
        onImagesChange(newImages);
    };

    const showImageOptions = () => {
        if (images.length >= maxImages) {
            Alert.alert(
                'Image Limit Reached',
                `You can only add up to ${maxImages} images.`
            );
            return;
        }
        setShowOptions(true);
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <ThemedText variant="subtitle" style={styles.title}>
                    Images of the product ({images.length}/{maxImages})
                </ThemedText>
                <TouchableOpacity
                    style={[styles.addButton, { backgroundColor: theme.primary }]}
                    onPress={showImageOptions}
                    disabled={images.length >= maxImages}
                >
                    <AntDesign name="plus" size={20} color="white" />
                </TouchableOpacity>
            </View>

            {images.length > 0 && (
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.imagesContainer}
                    contentContainerStyle={styles.imagesContent}
                >
                    {images.map((imageUri, index) => (
                        <View key={index} style={styles.imageWrapper}>
                            <Image source={{ uri: imageUri }} style={styles.image} />
                            <TouchableOpacity
                                style={[styles.removeButton, { backgroundColor: theme.error }]}
                                onPress={() => removeImage(index)}
                            >
                                <AntDesign name="close" size={16} color="white" />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
            )}

            {images.length === 0 && (
                <View style={[styles.emptyState, { backgroundColor: theme.inputBackground }]}>
                    <MaterialIcons name="photo-library" size={48} color={theme.text + '40'} />
                    <ThemedText variant="body" style={[styles.emptyText, { color: theme.text + '60' }]}>
                        No images selected
                    </ThemedText>
                    <ThemedText variant="body" style={[styles.emptySubtext, { color: theme.text + '40' }]}>
                        Tap the + button to add images
                    </ThemedText>
                </View>
            )}

            {/* Image Source Options Modal */}
            <Modal
                visible={showOptions}
                transparent
                animationType="fade"
                onRequestClose={() => setShowOptions(false)}
            >
                <Pressable style={styles.overlay} onPress={() => setShowOptions(false)}>
                    <View style={[styles.optionsModal, { backgroundColor: theme.cardBackground }]}>
                        <ThemedText variant="title" style={styles.modalTitle}>
                            Add Image
                        </ThemedText>

                        <TouchableOpacity
                            style={[styles.option, { borderBottomColor: theme.text + '20' }]}
                            onPress={pickImageFromCamera}
                        >
                            <MaterialIcons name="camera-alt" size={24} color={theme.primary} />
                            <ThemedText variant="body" style={styles.optionText}>
                                Take Photo
                            </ThemedText>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.option}
                            onPress={pickImageFromGallery}
                        >
                            <MaterialIcons name="photo-library" size={24} color={theme.primary} />
                            <ThemedText variant="body" style={styles.optionText}>
                                Choose from Gallery
                            </ThemedText>
                        </TouchableOpacity>
                    </View>
                </Pressable>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginVertical: 8,
        width: '100%',

    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    title: {
        fontSize: 16,
        fontWeight: '600',
    },
    addButton: {
        width: 32,
        height: 32,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    imagesContainer: {
        marginVertical: 8,
        paddingVertical: 8,
    },
    imagesContent: {
        paddingRight: 16,
    },
    imageWrapper: {
        position: 'relative',
        marginRight: 12,
    },
    image: {
        width: 100,
        height: 100,
        borderRadius: 8,
    },
    removeButton: {
        position: 'absolute',
        top: -8,
        right: -8,
        width: 24,
        height: 24,
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyState: {
        padding: 40,
        borderRadius: 12,
        alignItems: 'center',
        borderWidth: 2,
        borderStyle: 'dashed',
        borderColor: '#e0e0e0',
    },
    emptyText: {
        marginTop: 12,
        fontSize: 16,
        fontWeight: '500',
    },
    emptySubtext: {
        marginTop: 4,
        fontSize: 14,
        textAlign: 'center',
    },
    overlay: {
        flex: 1,
        top: 25,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        paddingHorizontal: 20,
    },
    optionsModal: {
        borderRadius: 12,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20,
        textAlign: 'center',
    },
    option: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
    },
    optionText: {
        marginLeft: 16,
        fontSize: 16,
    },
});
