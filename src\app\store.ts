import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../features/auth/authSlice';
import warehouseReducer from '../features/warehouses/warehouseSlice';
import orderReducer from '../features/orders/orderSlice';
import returnsReducer from '../features/returns/returnsSlice';
import collectionReducer from '../features/collections/collectionSlice';
import dashboardReducer from '../features/dashboard/dashboardSlice';
import modalsReducer from '../features/modal/modalsSlice';
// import productReducer from '../features/products/productSlice';

export const store = configureStore({
    reducer: {
        auth: authReducer,
        collections: collectionReducer,
        warehouses: warehouseReducer,
        orders: orderReducer,
        returns: returnsReducer,
        dashboard: dashboardReducer,
        modals: modalsReducer,
        // products: productReducer,

    },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
