import React, { useState, forwardRef } from 'react';
import {
    TextInput,
    StyleSheet,
    TextInputProps,
    View,
    TouchableOpacity,
} from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedIcon from './ThemedIcon';

interface ThemedTextInputProps extends TextInputProps {
    isPassword?: boolean;
    bgColor?: string;
    endComponent?: React.ReactNode;
}

const ThemedTextInput = forwardRef<TextInput, ThemedTextInputProps>(
    ({ isPassword = false, style, ...props }, ref) => {
        const theme = useTheme();
        const [secure, setSecure] = useState(isPassword);

        const toggleSecure = () => {
            setSecure(!secure);
        };

        return (
            <View style={[styles.container, { backgroundColor: props.bgColor || theme.inputBackground }]}>
                <TextInput
                    ref={ref}
                    placeholderTextColor={theme.text}
                    secureTextEntry={secure}
                    style={[
                        styles.input,
                        { color: theme.text },
                        style,
                        isPassword && { paddingRight: 40 }, // room for eye icon
                    ]}
                    {...props}
                />
                {props.endComponent}
                {isPassword && (
                    <TouchableOpacity onPress={toggleSecure} style={styles.icon}>
                        <ThemedIcon name={secure ? 'eye-off' : 'eye'} size={20} />
                    </TouchableOpacity>
                )}
            </View>
        );
    }
);

ThemedTextInput.displayName = 'ThemedTextInput';

export default ThemedTextInput;

const styles = StyleSheet.create({
    container: {
        width: '100%',
        borderRadius: 8,
        position: 'relative',
        justifyContent: 'center',
    },
    input: {
        padding: 12,
        fontSize: 16,
    },
    icon: {
        position: 'absolute',
        right: 12,
        top: '50%',
        marginTop: -10,
    },
});
