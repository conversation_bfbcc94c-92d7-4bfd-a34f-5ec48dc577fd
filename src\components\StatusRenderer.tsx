import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import useTheme from '../theme/useTheme';

export type StatusType = 'created' | 'processing' | 'shipped';

interface Props {
    status: string;
}

export default function StatusRenderer({ status }: Props) {
    const theme = useTheme();

    const getStatusStyle = () => {
        switch (status) {
            case 'created':
                return { backgroundColor: '#FFD70030', borderColor: "#FFD700", label: 'Created' }; // gold
            case 'processing':
                return { backgroundColor: '#1E90FF30', borderColor: "#1E90FF", label: 'Processing' }; // blue
            case 'intransit':
                return { backgroundColor: '#32CD3230', borderColor: "#32CD32", label: 'Shipped' }; // green
            default:
                return { backgroundColor: '#ccc30', borderColor: "#ccc", label: 'Unknown' };
        }
    };

    const { backgroundColor, borderColor, label } = getStatusStyle();

    return (
        <View style={[styles.chip, { backgroundColor, borderColor }]}>
            <Text style={[styles.text, { color: borderColor }]}>{label}</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    chip: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 20,
        borderWidth: 1,
        alignSelf: 'flex-start',
    },
    text: {
        fontSize: 12,
        fontWeight: 'bold',
    },
});
