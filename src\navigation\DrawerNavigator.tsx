import React, { useEffect, useRef, useState } from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import DashboardScreen from '../screens/DashboardScreen';
import ReturnsScreen from '../features/returns/ReturnsScreen';
import AddEditReturnScreen from '../features/returns/AddEditReturnScreen';
import CustomDrawerContent from '../components/CustomDrawerContent';
import { CollectionStackParamList, DrawerParamList, ReturnsStackParamList } from './type';
import useTheme from '../theme/useTheme';
import CollectionDetailsScreen from '../features/collections/CollectionDetailsScreen';
import CollectionListScreen from '../features/collections/CollectionListScreen';
import FulfillmentScreen from '../features/collections/FulfillmentScreen';
import ScanScreen from '../features/collections/ScanScreen';
import CollectionsScreen from '../screens/CollectionsScreen';
import AcknowledgmentScreen from '../features/collections/AcknowledgmentScreen';
import SignatureScreen from 'react-native-signature-canvas';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../app/store';
import { ActivityIndicator, Dimensions, Image, SafeAreaView, TouchableOpacity, View } from 'react-native';
import ThemedText from '../components/ThemedText';
import { FontAwesome6 } from '@expo/vector-icons';
import { SetSignature } from '../features/auth/authThunks';

const ReturnsStack = createNativeStackNavigator<ReturnsStackParamList>();
const RootStack = createNativeStackNavigator<CollectionStackParamList>();
const Drawer = createDrawerNavigator<DrawerParamList>();

function ReturnsStackNavigator() {
    return (
        <ReturnsStack.Navigator
            screenOptions={{ headerShown: false }}
            initialRouteName="ReturnsList"
        >
            <ReturnsStack.Screen name="ReturnsList" component={ReturnsScreen} />
            <ReturnsStack.Screen name="AddReturn" component={AddEditReturnScreen} />
            <ReturnsStack.Screen name="EditReturn" component={AddEditReturnScreen} />
        </ReturnsStack.Navigator>
    );
}

function RootNavigator() {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const { user } = useSelector((state: RootState) => state.auth);
    const [signatureBase64, setSignatureBase64] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const ref = useRef<any>(null);
    const [hasInk, setHasInk] = useState(false);
    const [noSignature, setNoSignature] = useState(false)

    useEffect(() => {
        // Check if user has a signature, if not, force the signature pad
        setNoSignature(!user.signature);

    }, [user])


    const handleSignature = async (signature: string) => {
        setSignatureBase64(signature);
        setIsLoading(true);
        if (signature) {
            const res = await dispatch(SetSignature(signature));
        }
        setIsLoading(false);
        // TODO: Dispatch to Redux or send to API
    };

    const handleEmpty = () => {
        setIsLoading(false);
    }

    const handleClear = () => {
        setHasInk(false);
        console.log('Signature cleared');
    };

    const handleError = (err: any) => {
        console.error('Signature pad error:', err);
        setIsLoading(false);
    };

    const handleEnd = () => {
        console.log('Signature drawing ended');
    };

    const handleSubmit = async () => {

    };


    if (noSignature) {
        //force the signature pad
        return <SafeAreaView style={{ flex: 1, padding: 20, gap: 24, flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: theme.background }}>
            {/* logo */}
            <View style={{ width: 100, height: 100, borderRadius: 10, overflow: 'hidden', backgroundColor: theme.cardBackground }}>
                {user.logo ? <Image source={{ uri: user.logo }} style={{ width: 100, height: 100, borderRadius: 50 }} />
                    : <FontAwesome6 name="user" size={50} color={theme.text} />}
            </View>
            <ThemedText variant="title" style={{ color: theme.text }}>
                Please sign to continue
            </ThemedText>
            <View style={{
                width: Dimensions.get('window').width * 0.9,
                height: 300,
                borderRadius: 8,
                overflow: 'hidden',
            }}>

                {hasInk && <TouchableOpacity style={{ position: 'absolute', top: 10, right: 10, zIndex: 1, padding: 10, borderRadius: 10, }} onPress={() => ref.current?.clearSignature()}>

                    <FontAwesome6 name="eraser" size={24} color={theme.error} />
                    <ThemedText style={{ color: theme.error, fontWeight: 'bold', fontSize: 12 }}>Clear</ThemedText>

                </TouchableOpacity>}
                <SignatureScreen
                    ref={ref}
                    onOK={handleSignature}
                    onEmpty={handleEmpty}
                    onClear={handleClear}
                    onEnd={handleEnd} // just log, don't submit
                    onError={handleError}
                    onBegin={() => setHasInk(true)}
                    autoClear={false}
                    descriptionText="Sign here"
                    clearText="Clear"
                    confirmText="Save"
                    penColor="#000"
                    backgroundColor="#fff"
                    webStyle={`.m-signature-pad--footer { display: none; }`}
                />

            </View>
            <View style={{ flexDirection: 'row', width: '100%', justifyContent: 'space-between' }}>

                <TouchableOpacity disabled={!hasInk} style={{ backgroundColor: hasInk ? theme.primary : theme.text + '30', flexDirection: 'row', gap: 10, flex: 1, padding: 10, height: 50, borderRadius: 10, justifyContent: 'center', alignItems: 'center' }} onPress={() => { setIsLoading(true); ref.current?.readSignature(); }}>
                    {isLoading ? (
                        <ActivityIndicator size="small" color='white' />
                    ) : (
                        <FontAwesome6 name="save" size={24} color='white' />
                    )}
                    <ThemedText style={{ color: 'white', fontWeight: 'bold' }}>Save & Continue</ThemedText>
                </TouchableOpacity>
            </View>

        </SafeAreaView >
    }

    return (
        <Drawer.Navigator
            id={"MainDrawer" as any}
            initialRouteName="Dashboard"
            drawerContent={(props) => <CustomDrawerContent {...props} />}
            screenOptions={{
                headerShown: false,
                drawerStyle: {
                    backgroundColor: theme.background,
                    width: 280,
                },
                drawerType: 'front',
                overlayColor: 'rgba(0, 0, 0, 0.5)',
            }}
        >
            <Drawer.Screen name="Dashboard" component={DashboardScreen} />
            <Drawer.Screen name="Collections" component={CollectionListScreen} />
            <Drawer.Screen name="Returns" component={ReturnsStackNavigator} />
        </Drawer.Navigator>
    );
}



export default function DrawerNavigator() {

    return (
        <RootStack.Navigator screenOptions={{ headerShown: false }} >
            <RootStack.Screen name="MainDrawer" component={RootNavigator} />
            <RootStack.Screen name="CollectionDetails" component={CollectionDetailsScreen} />
            <RootStack.Screen name="Fulfillment" component={FulfillmentScreen} />
            <RootStack.Screen name="Acknowledgment" component={AcknowledgmentScreen} />
        </RootStack.Navigator>


    );
}
