import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { RouteProp, useRoute } from '@react-navigation/native';
import CollectionDetailsScreen from '../features/collections/CollectionDetailsScreen';
import FulfillmentScreen from '../features/collections/FulfillmentScreen';
import { CollectionStackParamList, DrawerParamList } from '../navigation/type';
import AcknowledgmentScreen from '../features/collections/AcknowledgmentScreen';

const Stack = createNativeStackNavigator<CollectionStackParamList>();
type CollectionsScreenRouteProp = RouteProp<DrawerParamList, 'CollectionDetails'>;

export default function CollectionsScreen() {
    const route = useRoute<CollectionsScreenRouteProp>();
    const collectionId = route.params?.collectionId;

    return (

        <Stack.Navigator screenOptions={{ headerShown: false }} initialRouteName="CollectionDetails">
            <Stack.Screen name="CollectionDetails" component={CollectionDetailsScreen} initialParams={{ collectionId }} />
            <Stack.Screen name="Fulfillment" component={FulfillmentScreen} />
            <Stack.Screen name="Acknowledgment" component={AcknowledgmentScreen} />
        </Stack.Navigator>
    );
}
