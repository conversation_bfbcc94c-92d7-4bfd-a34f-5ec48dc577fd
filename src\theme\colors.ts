// theme.ts

export const LightTheme = {
    background: '#f2f2f2',         // Light gray background
    text: '#111827',               // Dark slate text
    inputBackground: '#F3F4F6',    // Light input background
    button: '#2563EB',             // Primary blue

    cardBackground: '#FFFFFF',

    primary: '#2563EB',            // Blue-600
    secondary: '#7C3AED',          // Violet-600
    tertiary: '#10B981',           // Emerald-500

    success: '#22C55E',            // Green-500
    warning: '#f0ad4e',            // Amber-500
    error: '#EF4444',              // Red-500
};

export const DarkTheme = {
    background: '#0F172A',         // Slate-900
    text: '#F8FAFC',               // Very light gray
    inputBackground: '#1E293B',    // Slate-800 input background
    button: '#3B82F6',             // Blue-500

    cardBackground: '#1E293B',

    primary: '#3B82F6',            // Blue-500
    secondary: '#8B5CF6',          // Violet-500
    tertiary: '#34D399',           // Emerald-400

    success: '#22C55E',            // Green-500
    warning: '#f0ad4e',            // Amber-400
    error: '#F87171',              // Red-400
};


export type ThemeType = typeof LightTheme;
