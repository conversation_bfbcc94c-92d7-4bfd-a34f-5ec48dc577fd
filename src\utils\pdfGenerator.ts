
import * as Print from 'expo-print';
import { shareAsync } from 'expo-sharing';
import { Alert } from 'react-native';
import { Collection, Order, OrderProduct } from '../navigation/type';
import dayjs from 'dayjs';

/**
 * Generate and share a PDF for a collection's products
 */
export async function generateCollectionDetailsPDF(
  collection: Collection | null,
  groupedProducts: {
    [sku: string]: {
      name: string;
      totalQty: number;
      items: { product: OrderProduct; orderNum: string }[];
    };
  }
): Promise<void> {
  if (!collection) return;

  try {
    const htmlContent = `
      <html>
      <head>
          <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .collection-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
              .collection-info { font-size: 14px; color: #666; margin-bottom: 20px; }
              .sku-group { margin-bottom: 25px; }
              .sku-header { font-size: 18px; font-weight: bold; margin-bottom: 10px; border-bottom: 2px solid #333; padding-bottom: 5px; }
              .product-row { display: flex; padding: 8px 0; border-bottom: 1px solid #eee; }
              .qty-col { width: 20%; font-weight: bold; }
              .name-col { width: 40%; }
              .order-col { width: 40%; }
              .table-header { font-weight: bold; background-color: #f5f5f5; padding: 10px 0; }
          </style>
      </head>
      <body>
          <div class="header">
              <div class="collection-title">Collection ${collection.num}</div>
              <div class="collection-info">
                  Status: ${collection.status} | Orders: ${collection.orders} |
                  Shipping: ${collection.shippingType} by ${collection.shippingBy}
              </div>
          </div>

          <div class="table-header product-row">
              <div class="qty-col">Quantity</div>
              <div class="name-col">Product Name</div>
              <div class="order-col">Order Number</div>
          </div>

          ${Object.entries(groupedProducts).map(([sku, { totalQty, items }]) => `
              <div class="sku-group">
                  <div class="sku-header">${totalQty} x ${sku}</div>
                  ${items.map(({ product, orderNum }) => `
                      <div class="product-row">
                          <div class="qty-col">${product.quantity}</div>
                          <div class="name-col">${product.name}</div>
                          <div class="order-col">${orderNum}</div>
                      </div>
                  `).join('')}
              </div>
          `).join('')}
      </body>
      </html>
    `;

    await Print.printAsync({ html: htmlContent });
  } catch (error) {
    console.error('Error generating PDF:', error);
    Alert.alert('Error', 'Failed to generate PDF. Please try again.');
  }
}
export const generateAcknowledgmentHtml = ({
  collection,
  orders,
  signatureBase64,
  logoUrl,
  companyName,
}: {
  collection: Collection | null;
  orders: Order[] | null;
  signatureBase64?: string | null;
  logoUrl?: string;
  companyName: string;
}) => {
  const today = new Date().toLocaleDateString('en-GB'); // format: DD/MM/YYYY
  const recipientName = collection?.confirmation?.recipient?.fullName || '[Delivery Name]';

  const orderList = orders
    ? orders
      .map((order, index) => `<li>${index + 1}. ${order.orderNum} - ${order.orderCode}</li>`)
      .join('')
    : '';

  const signatureImg = signatureBase64
    ? `<img src="${signatureBase64}" style="margin-top: 10px; height: 150px; width: 150px; border-radius: 6px;" />`
    : '';

  const headerLogo = logoUrl
    ? `<img src="${logoUrl}" alt="Company Logo" style="height: 50px; margin-right: 16px;" />`
    : '';

  return `
      <html>
        <head>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 24px;
              color: #000;
            }
            .header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 24px;
              border-bottom: 1px solid #ccc;
              padding-bottom: 10px;
            }
            .company-info {
              display: flex;
              flex-direction: column;
            }
            .company-name {
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 4px;
            }
            .meta-info {
              font-size: 14px;
              color: #555;
            }
            .card {
              padding: 24px;
            }
            h2 {
              margin-bottom: 16px;
            }
            p {
              margin-bottom: 16px;
              line-height: 1.6;
              font-size: 14px;
            }
            ul {
              margin: 0 0 16px 30px;
              padding: 0;
            }
          </style>
        </head>
        <body>
          <div class="header">
            
            <div class="company-info">
              <div class="company-name">${companyName}</div>
              <div class="meta-info">
                Collection No: <strong>${collection?.num || '-'}</strong><br />
                Shipping Type: <strong>${collection?.shippingType || '-'}</strong><br />
                Created At: <strong>${collection?.addedAt ? new Date(collection.addedAt).toLocaleDateString('en-GB') : '-'}</strong>
              </div>
            </div>
            ${headerLogo}
          </div>
  
          <div class="card">
            <h2 style="text-align: center;">Delivery Acknowledgment</h2>
            <p>
              I, <strong>${recipientName}</strong>, hereby acknowledge that I have received all the items listed under Collection No. <strong>${collection?.num}</strong>,
              prepared by the warehouse team of <strong>${companyName}</strong> on <strong>${today}</strong>.
            </p>
            <p style="text-align: center;"><strong>I confirm that:</strong></p>
            <p>• I have reviewed and verified the following orders included in this collection:</p>
            <ul>${orderList}</ul>
            <p>• The items are complete and in acceptable condition at the time of receipt.</p>
            <p>• I take full responsibility for delivering the orders to the intended recipients as listed in the associated documents.</p>
            <p>• I understand that any loss, damage, or discrepancy during delivery may result in an internal investigation and possible accountability.</p>
            <p>Signed electronically on <strong>${today}</strong>, by:</p>
            <p><strong>Recipient:</strong> ${recipientName}</p>
            ${signatureImg}
          </div>
        </body>
      </html>
    `;
};


export async function generateStyledCollectionDetailsPDF(
  collection: Collection,
  orders: Order[]
): Promise<void> {
  if (!collection) return;

  try {
    const details = [
      ['Number', collection.num],
      ['Status', collection.status],
      ['Shipping Type', collection.shippingType],
      ['Shipping By', collection.shippingBy],
      ['Added At', dayjs(collection.addedAt).format('DD/MM/YYYY')],
    ];

    const htmlContent = `
      <html>
      <head>
        <style>
          body { font-family: Calibri, sans-serif; padding: 20px; color: #111827; }
          h1 { text-align: center; background-color: #111827; color: #fff; padding: 10px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
          th, td { border: 1px solid #111827; padding: 8px; text-align: center; }
          .gray-row { background-color: #D9D9D9; }
          .dark-header { background-color: #111827; color: white; font-weight: bold; }
          .section-title { background-color: #1E293B; color: white; font-weight: bold; text-align: center; padding: 10px; margin-top: 30px; }
        </style>
      </head>
      <body>
        <h1>Collection Details</h1>

        <table>
          ${details.map(([label, value]) => `
            <tr>
              <td><strong>${label}</strong></td>
              <td class="gray-row" colspan="2">${value}</td>
            </tr>
          `).join('')}
        </table>

        
        <div class="section-title">Orders</div>

        <table>
          <tr class="dark-header">
            <th>Order #</th>
            <th>Tracking Number</th>
            <th>Shipping Company</th>
            <th>Contact</th>
            <th>Store</th>
            <th>Status</th>
          </tr>
          ${orders.map(order => `
            <tr>
              <td>${order.orderNum || '-'}</td>
              <td>${order.trackingNumber || '-'}</td>
              <td>${order.shippingCompany || '-'}</td>
              <td>${order.contact || '-'}</td>
              <td>${order.store || '-'}</td>
              <td>${order.status || '-'}</td>
            </tr>
          `).join('')}
        </table>
      </body>
      </html>
    `;

    await Print.printAsync({ html: htmlContent });
  } catch (error) {
    console.error('Error generating PDF:', error);
    Alert.alert('Error', 'Failed to generate PDF. Please try again.');
  }
}
