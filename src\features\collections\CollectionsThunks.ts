// src/features/collections/collectionsThunks.ts
import { createAsyncThunk } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';
import { RootState } from '../../app/store';

export const fetchCollectionsByWarehouseId = createAsyncThunk(
    'collections/fetchByWarehouseId',
    async (warehouseId: number | undefined, { getState, rejectWithValue }) => {
        if (!warehouseId) {
            return rejectWithValue('Warehouse id is required');
        }
        try {
            const state = getState() as RootState;
            const { filter } = state.collections;
            let query = `warehouseId=${warehouseId}`;
            if (filter.startDate) {
                query += `&startDate=${filter.startDate}`;
            }
            if (filter.endDate) {
                query += `&endDate=${filter.endDate}`;
            }
            if (filter.status && filter.status !== 'All') {
                query += `&status=${filter.status}`;
            }
            if (filter.keyword) {
                query += `&keyword=${filter.keyword}`;
            }
            const response = await axiosClient.get(`/collections?${query}`);

            return response.data.result;
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error fetching collections');
        }
    }
);

//set confirmation delivery
export const setConfirmationDelivery = createAsyncThunk(
    'collections/setConfirmationDelivery',
    async ({ collectionId, receiver, signature }: { collectionId: number, receiver: string, signature: string | null }, { rejectWithValue }) => {
        try {
            const response = await axiosClient.put(`/collections/${collectionId}/receiver`, { receiverName: receiver, signature });

            return [collectionId, response.data.result];
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error setting confirmation delivery');
        }
    }
);

export const getRecipients = createAsyncThunk(
    'collections/getRecipients',
    async (collectionId: number, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get(`/collections/${collectionId}/recipients`);
            return response.data.result;
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error fetching recipients');
        }
    }
);

//GETCOLLECTIONbY ID 
export const fetchCollectionById = createAsyncThunk(
    'collections/fetchById',
    async (collectionId: number, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get(`/collections?id=${collectionId}`);
            return response.data.result; // Adjust depending on API structure
        } catch (err: any) {
            console.log(err.response?.data);

            return rejectWithValue(err.response?.data || 'Error fetching collection');
        }
    }
);

//get all product of a collection 
export const fetchCollectionProducts = createAsyncThunk(
    'collections/fetchProducts',
    async (collectionId: number, { rejectWithValue }) => {
        try {
            const response = await axiosClient.get(`/products?collectionId=${collectionId}`);
            return response.data.result; // Adjust depending on API structure
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error fetching collection products');
        }
    }
);


//update collection Status
export const updateCollectionStatus = createAsyncThunk(
    'collections/updateStatus',
    async ({ collectionId, status }: { collectionId: number; status: string }, { getState, rejectWithValue }) => {
        try {

            const response = await axiosClient.put(`/collections/${collectionId}/status/${status}`);
            return response.data;
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error updating collection status');
        }
    }
);


