
import React from 'react';
import { GestureResponderEvent, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import StatusRenderer from './StatusRenderer';
import { Entypo, Feather, FontAwesome, FontAwesome6, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Order } from '../navigation/type';
import OrderStatus from './OrderStatus';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { BarcodeView } from 'rn-barcode-renderer';

dayjs.extend(advancedFormat);
dayjs.extend(relativeTime);

type Props = {
    order: Order;
    onPress: (event: GestureResponderEvent) => void;
    onOptionClick: () => void;
    selected: boolean;
};

export default function OrderCard({ order, onPress, onOptionClick, selected }: Props) {
    const theme = useTheme();

    return (
        <View style={{ width: '100%' }} >

            <View
                style={[styles.button, { backgroundColor: selected ? theme.primary + '20' : theme.cardBackground }]}
            >
                <View style={{ flex: 1, width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >

                    <OrderStatus orderStatus={order.status} />
                    {/* three point settings  */}
                    <TouchableOpacity style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center', borderRadius: 10 }}>
                        <ThemedText variant="body">See Details</ThemedText>
                        <Entypo name="chevron-right" size={24} color={theme.text} />
                        {/* {selected ? <Feather name="check-square" size={24} color={theme.text} /> : <Feather name="square" size={24} color={theme.text} />} */}
                    </TouchableOpacity>
                </View>
                <View style={{ flex: 1, width: '100%', marginTop: 10, display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >
                    <ThemedText variant='title'>{order.orderNum}</ThemedText>
                    <ThemedText variant='body'>{order.orderCode}</ThemedText>
                </View>

                <View style={{ flex: 1, width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >
                    <ThemedText variant='body'>{dayjs(order.createdAt).format('dddd, DD MMM YYYY · HH:mm')}</ThemedText>

                </View>
                {/* seperator */}
                <View style={{ width: '100%', height: 1, backgroundColor: theme.text + '40', marginVertical: 10 }} />
                <View style={{ flex: 1, width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >
                    <View style={{ display: 'flex', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center' }}>
                        <MaterialIcons name="flight" size={24} color={theme.text} />
                        <ThemedText variant='body'>{order.trackingNumber}</ThemedText>
                    </View>
                    <TouchableOpacity onPress={onOptionClick} style={{ display: 'flex', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center' }}>
                        <FontAwesome name="cube" size={20} color={theme.text} />
                        <ThemedText variant='body'>{order.ordersProducts.length} Products</ThemedText>
                        <Entypo name="chevron-down" size={24} color={theme.text} />
                    </TouchableOpacity>
                </View>
                {selected
                    && <View style={{ flex: 1, width: '100%', display: 'flex', flexDirection: 'column', marginTop: 10, justifyContent: 'space-between', alignItems: 'center' }} >
                        <View style={{ width: '100%', height: 1, backgroundColor: theme.text + '40', marginVertical: 10 }} />
                        <View style={{ display: 'flex', width: '100%', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center' }}>
                            <ThemedText variant='body' style={{ width: "20%" }}>Qty</ThemedText>
                            <ThemedText variant='body' style={{ width: "40%" }}>SKU</ThemedText>
                            <ThemedText variant='body' style={{ width: "40%" }}>Name</ThemedText>
                        </View>
                        <View style={{ width: '100%', height: 1, backgroundColor: theme.text + '40', marginVertical: 10 }} />
                        {
                            order.ordersProducts.map((product, index) => (
                                <View key={index} style={{ display: 'flex', width: '100%', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center' }}>
                                    <ThemedText variant='body' style={{ width: '20%' }}>{product.quantity}</ThemedText>
                                    <ThemedText variant='body' style={{ width: "40%" }}>{product.sku}</ThemedText>
                                    <ThemedText variant='body' style={{ width: "40%" }}>{product.name}</ThemedText>
                                </View>
                            ))

                        }

                    </View>}

            </View>

            {/* option flex container */}
            {/* {selected && (
                <Animated.View entering={FadeInDown.delay(0).duration(200)} style={{
                    width: '100%',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: 5,
                    marginTop: 10,
                }}>
                    <View style={{ flex: 1, gap: 5 }}>
                        <Animated.View entering={FadeInDown.delay(50).duration(300)}>
                            <TouchableOpacity style={styles.option}>
                                <MaterialCommunityIcons name="file-excel" size={24} color={theme.text} />
                                <ThemedText variant="body">Export</ThemedText>
                            </TouchableOpacity>
                        </Animated.View>

                        <Animated.View entering={FadeInDown.delay(100).duration(300)}>
                            <TouchableOpacity style={styles.option}>
                                <Entypo name="print" size={24} color={theme.text} />
                                <ThemedText variant="body">Print</ThemedText>
                            </TouchableOpacity>
                        </Animated.View>
                    </View>

                    <View style={{ flex: 1, gap: 10 }}>
                        <Animated.View entering={FadeInDown.delay(150).duration(300)}>
                            <TouchableOpacity style={styles.option}>
                                <MaterialIcons name="summarize" size={24} color={theme.text} />
                                <ThemedText variant="body">Print Summary</ThemedText>
                            </TouchableOpacity>
                        </Animated.View>

                        <Animated.View entering={FadeInDown.delay(200).duration(300)}>
                            <TouchableOpacity style={styles.option}>
                                <FontAwesome name="print" size={24} color={theme.text} />
                                <ThemedText variant="body">Print Labels</ThemedText>
                            </TouchableOpacity>
                        </Animated.View>
                    </View>
                </Animated.View>
            )} */}


        </View>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        padding: 14,
        alignItems: 'center',
    },
    text: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    option: {
        height: 40,
        display: 'flex',
        flexDirection: 'row',
        gap: 10,
        alignItems: 'center',
    },
});
