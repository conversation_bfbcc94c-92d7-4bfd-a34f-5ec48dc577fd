import React, { useEffect, useRef, useState } from 'react';
import {
    View,
    FlatList,
    StyleSheet,
    ScrollView,
    Dimensions,
    ActivityIndicator,
    RefreshControl,
    LayoutChangeEvent,
    Text,
    Platform,
    KeyboardAvoidingView,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons, AntDesign, MaterialIcons, FontAwesome6, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withSpring,
    Easing,
    withRepeat,
    withSequence,
} from 'react-native-reanimated';
import ThemedText from '../../components/ThemedText';
import useTheme from '../../theme/useTheme';
import { Collection, Confirmation, Order, OrderProduct } from '../../navigation/type';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../app/store';
import { changeOrderStatus, fetchOrders, toggleProductStatus } from '../orders/orderSlice';
import { TouchableOpacity as guesterTouchableOpacity } from 'react-native-gesture-handler';
import { TouchableOpacity as reactTouchableOpacity } from 'react-native';

import { CollectionStackParamList } from '../../navigation/type';
import { getRecipients, setConfirmationDelivery } from './CollectionsThunks';
import { LinearGradient } from 'expo-linear-gradient';
import GlobalLayout from '../../components/GlobalLayout';
import { Modalize } from 'react-native-modalize';
import { Camera, CameraView } from 'expo-camera';
import { useAudioPlayer } from 'expo-audio';
import { useToast } from '../toast/ToastContext';
import ThemedTextInput from '../../components/ThemedTextInput';
import RenderEmptyState from '../../components/RenderEmptyState';
const SuccessAudioSource = require('../../../assets/sounds/success.mp3');
const ErrorAudioSource = require('../../../assets/sounds/error.mp3');

type FulfillmentRouteProp = RouteProp<CollectionStackParamList, 'Fulfillment'>;
type NavigationProp = NativeStackNavigationProp<CollectionStackParamList, 'Fulfillment'>;

const TouchableOpacity = Platform.OS === 'ios' ? reactTouchableOpacity : guesterTouchableOpacity;


export default function FulfillmentScreen() {
    const { params } = useRoute<FulfillmentRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const theme = useTheme();
    const { collectionId, action } = params;
    const dispatch = useDispatch<AppDispatch>();

    // Get orders from Redux state
    const { orders: reduxOrders, loading, error, statusKeys, status: orderStatus } = useSelector((state: RootState) => state.orders);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const { collections, loading: collectionLoading, recipients } = useSelector((state: RootState) => state.collections);

    // Local state for orders to avoid refetching on every toggle
    const [localOrders, setLocalOrders] = useState<Order[]>([]);
    const [currentOrderIndex, setCurrentOrderIndex] = useState(0);
    const [refreshing, setRefreshing] = useState(false);
    const prevWarehouseId = useRef<number | null>(null);
    const latestOrdersRef = useRef<Order[]>([]);
    const [confirmation, setConfirmation] = useState<Confirmation | null>(null);

    const modalRef = useRef<Modalize>(null);
    const modalRecipientRef = useRef<Modalize>(null);
    const [hasPermission, setHasPermission] = useState<boolean | null>(null);
    const [scanned, setScanned] = useState(false);
    const [isScanning, setIsScanning] = useState(false);
    const [scanSuccess, setScanSuccess] = useState(false);
    const [scanError, setScanError] = useState<string | null>(null);
    const [torchOn, setTorchOn] = useState(false);
    const { showToast } = useToast();
    const [receiverName, setReceiverName] = useState('');
    const [loadingRecipient, setLoadingRecipient] = useState(false)

    useEffect(() => {

        if (collectionId && collections.length > 0) {
            const collection = collections.find(col => col.id === collectionId);
            if (collection) {

                setConfirmation(collection.confirmation);
            }
        } else {
            setConfirmation(null);
        }

    }, [collectionId, collections]);



    const toggleTorch = () => {
        setTorchOn((prev) => !prev);
    };
    const SuccessPlayer = useAudioPlayer(SuccessAudioSource);
    const ErrorPlayer = useAudioPlayer(ErrorAudioSource);
    // Audio feedback functions
    const playSuccessSound = async () => {
        try {
            SuccessPlayer.seekTo(0);
            SuccessPlayer.play();
        } catch (err) {
            console.warn('Failed to play success sound', err);
        }
    };

    const playErrorSound = async () => {
        try {
            ErrorPlayer.seekTo(0);
            ErrorPlayer.play();
        } catch (err) {
            console.warn('Failed to play error sound', err);
        }
    };

    const [scanMessage, setscanMessage] = useState<string | null>(null);
    useEffect(() => {
        if (!scanMessage) return;

        const timeout = setTimeout(() => {
            setscanMessage(null);
        }, 5000); // 5 seconds

        return () => clearTimeout(timeout); // Clear previous timeout if scanMessage changes
    }, [scanMessage]);


    const handleBarcodeScanned = async (scanningResult: { type: string; data: string }) => {
        setScanned(true);

        const scannedOrder = localOrders.find(order => order.orderCode === scanningResult.data);
        if (scannedOrder) {
            if (scannedOrder.status === statusKeys[action === 'fullfillment' ? 1 : 2]) {
                setscanMessage(`Already scanned: ${scanningResult.data}`);
                setScanSuccess(false);
                // showToast('Already scanned', 'error');
                playErrorSound();
            } else {

                setscanMessage(`Scanned: ${scanningResult.data}`);
                await dispatch(changeOrderStatus({ orderId: scannedOrder.id, type: action === 'fullfillment' ? 1 : 2 }));
                playSuccessSound();
                setScanError(null);
                setScanSuccess(true);

            }

        } else {
            setscanMessage(`Order not found`);
            setScanSuccess(false);
            // showToast('Order not found', 'error');
            playErrorSound();
        }

        // Re-enable scanner after 1.5s
        setTimeout(() => {
            setScanned(false);
        }, 1500);
    };

    const scanLineTranslateY = useSharedValue(0);

    useEffect(() => {
        scanLineTranslateY.value = withRepeat(
            withTiming(240, { duration: 1000 }),
            -1,
            true
        );
    }, []);
    const scanLineStyle = useAnimatedStyle(() => {
        return {
            transform: [{ translateY: scanLineTranslateY.value }],
        };
    });

    // Request camera permissions
    useEffect(() => {
        const getCameraPermissions = async () => {
            const { status } = await Camera.requestCameraPermissionsAsync();
            setHasPermission(status === 'granted');
        };

        getCameraPermissions();
    }, []);

    useEffect(() => {
        if (selectedWarehouse?.id && prevWarehouseId.current && selectedWarehouse.id !== prevWarehouseId.current) {
            navigation.goBack();
        }

        if (selectedWarehouse?.id) {
            prevWarehouseId.current = selectedWarehouse.id;
        }
    }, [selectedWarehouse?.id]);

    // Animation values
    const slideAnimation = useSharedValue(0);



    // Fetch orders on component mount
    useEffect(() => {
        dispatch(fetchOrders({ collectionId, warehouseId: null }));
    }, [dispatch, collectionId]);

    // Sync Redux orders with local state
    useEffect(() => {
        if (reduxOrders.length > 0) {
            setLocalOrders(reduxOrders);
        }
    }, [reduxOrders]);


    // Refresh function for pull-to-refresh
    const onRefresh = async () => {
        setRefreshing(true);
        try {
            await dispatch(fetchOrders({ collectionId, warehouseId: null }));
        } catch (error) {
            console.error('Error refreshing orders:', error);
        } finally {
            setRefreshing(false);
        }
    };
    useEffect(() => {
        if (localOrders.length > 0) {
            latestOrdersRef.current = localOrders;
            checkForConfirmation();
        }
    }, [localOrders]);

    const checkForConfirmation = () => {
        const allScanned = localOrders.every(order => order.status === statusKeys[action === 'fullfillment' ? 1 : 2]);
        if (allScanned) {


            if (action === 'shippement') {

                if (confirmation?.recipient?.fullName) {
                    navigation.navigate('Acknowledgment', { collectionId, orders: localOrders });
                    // showToast('All orders scanned. Please proceed to confirm reception.', 'info');
                }

            } else {
                navigation.goBack();
            }

            // modalRef.current?.close();
        }
    }

    // Handle slide animation when order index changes
    useEffect(() => {
        slideAnimation.value = withTiming(0, { duration: 300 });
    }, [currentOrderIndex]);


    const saveReceiver = () => {
        if (!receiverName) return;
        dispatch(setConfirmationDelivery({ collectionId, receiver: receiverName, signature: null }));
    }


    return (
        <GlobalLayout title="Fulfillment" isInner>

            {/* Header with return arrow and SKU */}
            <View style={[styles.header, { borderBottomColor: theme.text + '50' }]}>

                <ThemedText variant="subtitle" style={styles.headerTitle}>
                    ({localOrders.filter(o => o.status === statusKeys[action === 'fullfillment' ? 1 : 2]).length}/{localOrders.length}) orders
                </ThemedText>
                {confirmation && confirmation.recipient?.fullName && <TouchableOpacity
                    onPress={() => { modalRef.current?.open(); }}
                    style={[styles.backButton, { backgroundColor: theme.success, borderRadius: 8 }]}
                >
                    <ThemedText variant="subtitle" style={{ color: theme.cardBackground, fontWeight: 'bold' }}>
                        Scan
                    </ThemedText>
                    <MaterialCommunityIcons name="barcode-scan" size={24} color="white" />

                </TouchableOpacity>}
            </View>

            <ScrollView refreshControl={
                <RefreshControl
                    refreshing={refreshing}
                    onRefresh={onRefresh}
                    colors={[theme.primary || '#007AFF']}
                    tintColor={theme.primary || '#007AFF'}
                />
            } style={styles.content}>
                <View style={styles.taskContainer}>
                    {(!confirmation || !confirmation.recipient?.fullName) && <View style={{ flex: 1, width: '100%' }} >
                        <TouchableOpacity onPress={async () => {
                            setLoadingRecipient(true);
                            const res = await dispatch(getRecipients(collectionId));
                            if (getRecipients.fulfilled.match(res)) {

                                modalRecipientRef.current?.open();
                            }
                            setLoadingRecipient(false);

                        }} style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: theme.cardBackground, borderRadius: 8, paddingHorizontal: 16 }}>
                            <ThemedText variant="subtitle" style={{ marginVertical: 16, alignSelf: 'flex-start', textAlign: 'left' }}>
                                Please select the receiver
                            </ThemedText>
                            {loadingRecipient ? <ActivityIndicator size="small" color={theme.primary} /> : <AntDesign name="down" size={24} color={theme.primary} />}
                        </TouchableOpacity>

                        <ThemedText variant="subtitle" style={{ marginVertical: 16, alignSelf: 'flex-start', textAlign: 'left' }}>
                            Or, enter new receiver's name :
                        </ThemedText>
                        <ThemedTextInput
                            bgColor={theme.cardBackground}
                            placeholder="Ex: John Doe"
                            value={receiverName}
                            onChangeText={setReceiverName}
                            keyboardType="default"
                            autoCapitalize="none"
                            returnKeyType="go"
                            onSubmitEditing={saveReceiver}
                        />
                        <TouchableOpacity
                            onPress={saveReceiver}
                            style={{
                                flexDirection: 'row',
                                paddingVertical: 10,
                                paddingHorizontal: 16,
                                borderRadius: 8,
                                marginVertical: 10,
                                backgroundColor: theme.primary,
                                alignSelf: 'flex-end', // Let the width wrap content
                            }}
                        >
                            {collectionLoading && (
                                <ActivityIndicator
                                    size="small"
                                    color={theme.background}
                                    style={{ marginRight: 8 }}
                                />
                            )}
                            <ThemedText style={{ color: 'white', textAlign: 'center' }}>
                                Save & Continue
                            </ThemedText>
                        </TouchableOpacity>
                    </View>}

                    {confirmation && confirmation.recipient?.fullName &&
                        [...localOrders].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()).map((order, index) => {
                            const isScanned = order.status === (action === 'fullfillment' ? statusKeys[1] : statusKeys[2]);
                            return (
                                // <SwipeableBarCodeRow key={order.id} currentOrder={order} onPress={() => { }} theme={theme} />
                                <View style={[styles.orderDetailsCard, { backgroundColor: isScanned ? theme.success + '30' : theme.cardBackground, borderColor: theme.success, borderWidth: isScanned ? 2 : 0 }]} key={order.id}>
                                    <View style={{ flexDirection: 'row', gap: 16, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                                        <View style={{ flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                                            <View style={{ flexDirection: 'row', gap: 8, justifyContent: 'flex-start', alignItems: 'center' }}>
                                                <ThemedText variant="body" style={styles.orderNumber}>
                                                    #{order.orderNum}
                                                </ThemedText>
                                                {/* <OrderStatus orderStatus={order.status} /> */}

                                            </View>
                                            <ThemedText variant="body" style={styles.orderCode}>
                                                {order.orderCode}
                                            </ThemedText>
                                        </View>
                                    </View>
                                    {/* <OrderStatus orderStatus={order.status} /> */}
                                    <ThemedText variant="title" style={{ color: isScanned ? theme.success : theme.text + '50', fontSize: 18, fontStyle: 'italic', fontWeight: 'bold' }}>
                                        {isScanned ? 'Scanned' : 'Not Scanned'}
                                    </ThemedText>
                                    {/* {isScanned ? <MaterialIcons name="radio-button-checked" size={34} color={theme.success} /> :
                                        <MaterialIcons name="radio-button-unchecked" size={34} color={theme.text + '50'} />} */}
                                </View>)
                        }
                        )
                    }</View>
            </ScrollView>
            <Modalize
                ref={modalRef}
                modalHeight={Dimensions.get('window').height * 0.6}

                modalStyle={[styles.modal, { backgroundColor: theme.cardBackground }]}
                handleStyle={{ backgroundColor: theme.primary }}
                withHandle
            ><View style={[styles.scannerContainer, { height: Dimensions.get('window').height * 0.6 }]}>

                    <View style={StyleSheet.absoluteFillObject}>
                        <CameraView
                            enableTorch={torchOn}
                            style={StyleSheet.absoluteFill}
                            facing="back"
                            barcodeScannerSettings={{
                                barcodeTypes: [
                                    "qr", "pdf417", "code128", "code39", "code93",
                                    "codabar", "ean13", "ean8", "upc_e", "upc_a"
                                ],
                            }}
                            onBarcodeScanned={scanned ? undefined : handleBarcodeScanned}
                        />
                    </View>
                    <TouchableOpacity
                        style={styles.torchButton}
                        onPress={toggleTorch}
                    >
                        <Ionicons name={torchOn ? 'flash' : 'flash-off'} size={24} color="white" />
                    </TouchableOpacity>
                    {scanMessage && <View style={[styles.scanMessage, { backgroundColor: scanSuccess ? theme.success : theme.error }]}>
                        <ThemedText variant="body" style={{ color: 'white' }}>
                            {scanMessage}
                        </ThemedText>

                    </View>}

                    <View style={styles.scanOverlay}>

                        <View style={styles.scanFrame} >

                            {scanned ? <ActivityIndicator size="large" color={theme.primary} /> : <Animated.View style={[styles.scanLine, scanLineStyle]}>
                                <LinearGradient
                                    colors={['transparent', 'rgba(255,0,0,0.7)', 'transparent']}
                                    style={{ flex: 1 }}
                                    start={[0, 0]}
                                    end={[1, 0]}
                                />
                            </Animated.View>}

                        </View>

                    </View>

                </View>
            </Modalize>
            <Modalize
                ref={modalRecipientRef}
                modalStyle={styles.modal}
                modalHeight={Dimensions.get('window').height * 0.6}
                withHandle

                flatListProps={{
                    data: recipients,
                    ListEmptyComponent: () => <RenderEmptyState state="Recipient" />,
                    keyExtractor: (item) => item.id.toFixed(0),
                    renderItem: ({ item }) => (
                        <TouchableOpacity
                            onPress={() => {
                                dispatch(setConfirmationDelivery({ collectionId, receiver: item.fullName, signature: item.signature }));
                                modalRecipientRef.current?.close(); // <- fix: should be modalRecipientRef
                            }}
                            style={styles.recipientItem}
                        >
                            <ThemedText variant="body">{item.fullName}</ThemedText>
                            <ThemedText variant="body" style={{ color: item.signature ? theme.success : theme.error, fontWeight: 'bold' }}>{item.signature ? 'Signed' : 'Not Signed'}</ThemedText>
                        </TouchableOpacity>
                    ),
                    ListHeaderComponent: (
                        <ThemedText variant="title" style={styles.modalTitle}>
                            Select Receiver
                        </ThemedText>
                    ),
                    contentContainerStyle: styles.modalContent,
                }}
            />

        </GlobalLayout>
    );
}

const styles = StyleSheet.create({
    scanLine: {
        position: 'absolute',
        width: '90%',
        zIndex: 1,
        height: 4,
        borderRadius: 2,
        overflow: 'hidden',
        top: 0,
        alignSelf: 'center',
    },
    recipientItem: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    torchButton: {
        position: 'absolute',
        top: 16,
        right: 16,
        zIndex: 2000,
        padding: 8,
        borderRadius: 8,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    scanMessage: {
        position: 'absolute',
        top: 16,
        left: 16,
        zIndex: 2000,
        padding: 8,
        borderRadius: 8,
    },
    scannerContainer: {
        flex: 1,
        width: '100%',
        position: 'relative',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        overflow: 'hidden',
    },
    scanner: {
        flex: 1,
    },
    scanOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    scanFrame: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: 250,
        height: 250,
        borderWidth: 2,
        borderColor: '#007AFF',
        borderRadius: 12,
        backgroundColor: 'transparent',
    },
    modal: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    modalContent: {
        padding: 20,
    },
    modalTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    headerSection: {
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        paddingVertical: 16,
    },
    headerModal: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
    },
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 16,
        paddingHorizontal: 20,
        borderBottomWidth: 1,
    },
    backButton: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 10,
        padding: 8,
    },
    headerTitle: {
        textAlign: 'center',
        fontSize: 16,
        fontWeight: 'bold',
    },
    progressContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    progressBackground: {
        height: 4,
        backgroundColor: '#e0e0e0',
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    progressText: {
        fontSize: 12,
        textAlign: 'center',
        opacity: 0.7,
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    taskContainer: {
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: 16,
        borderRadius: 16,
        paddingTop: 16,
    },
    orderDetailsCard: {
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderRadius: 16,
    },
    orderHeader: {

    },
    orderInfo: {
        flex: 1,
    },
    orderNumber: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    orderCode: {
        fontSize: 14,
        opacity: 0.7,
        marginBottom: 2,
    },
    orderStatus: {
        fontSize: 14,
        opacity: 0.7,
    },
    orderActions: {
        flexDirection: 'row',
        gap: 8,
    },
    iconButton: {
        width: 44,
        height: 44,
        borderRadius: 22,
        justifyContent: 'center',
        alignItems: 'center',
    },
    goodsDescription: {
        fontSize: 14,
        lineHeight: 20,
        opacity: 0.8,
    },
    productsContainer: {
        flex: 1,
    },
    productsTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    productsList: {
        flex: 1,
    },
    productRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 8,
        borderBottomWidth: 1,
    },
    productQty: {
        width: '20%',
        alignItems: 'center',
    },
    qtyText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    productName: {
        width: '70%',
        paddingHorizontal: 12,
    },
    nameText: {
        fontSize: 14,
        lineHeight: 18,
    },
    checkedText: {
        textDecorationLine: 'line-through',
        opacity: 0.6,
    },
    productCheckbox: {
        width: '10%',
        alignItems: 'center',
    },
    footer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: 16,
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderTopWidth: 1,
    },
    orderIndicator: {
        flex: 1,
        alignItems: 'center',
    },
    indicatorText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    checkboxRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    skeletonContainer: {
        paddingVertical: 16,
        alignItems: 'center',
    },

    nextButtonText: {
        fontSize: 16,
        fontWeight: '600',
        margin: 'auto'
    },
});
