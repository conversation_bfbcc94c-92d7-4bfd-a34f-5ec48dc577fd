import React, { useState } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import MainLayout from '../components/MainLayout';
import CollectionListScreen from '../features/collections/CollectionListScreen';
import CollectionDetailsScreen from '../features/collections/CollectionDetailsScreen';
import FulfillmentScreen from '../features/collections/FulfillmentScreen';
import ScanScreen from '../features/collections/ScanScreen';
import { CollectionStackParamList } from '../navigation/type';

const CollectionStack = createNativeStackNavigator<CollectionStackParamList>();

type TabType = 'collections' | 'returns';

export default function MainAppScreen() {
    const [activeTab, setActiveTab] = useState<TabType>('collections');

    const handleTabChange = (tab: TabType) => {
        setActiveTab(tab);
    };

    const renderTabContent = () => {
        // Only handle collections tab now, returns is in DrawerNavigator
        return (
            <CollectionStack.Navigator
                screenOptions={{ headerShown: false }}
                initialRouteName="CollectionList"
            >
                <CollectionStack.Screen name="CollectionList" component={CollectionListScreen} />
                <CollectionStack.Screen name="CollectionDetails" component={CollectionDetailsScreen} />
                <CollectionStack.Screen name="Fulfillment" component={FulfillmentScreen} />
                <CollectionStack.Screen name="ScanScreen" component={ScanScreen} />
            </CollectionStack.Navigator>
        );
    };

    return (
        <MainLayout activeTab={activeTab} onTabChange={handleTabChange}>
            {renderTabContent()}
        </MainLayout>
    );
}
