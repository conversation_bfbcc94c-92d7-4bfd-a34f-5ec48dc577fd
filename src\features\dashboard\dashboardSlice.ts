
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axiosClient from '../../api/axiosClient';
import { Collection, Order } from '../../navigation/type';
import { RootState } from '../../app/store';

interface DashboardFilter {
    startDate: string;
    endDate: string;
    status: string;
}
// Define the shape of the slice state
interface DashboardState {
    dashbaordCollects: Collection[];
    dashboardOrders: Order[];
    filter: DashboardFilter;
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: DashboardState = {
    dashbaordCollects: [],
    dashboardOrders: [],
    filter: {
        startDate: '',
        endDate: '',
        status: '',
    },
    loading: false,
    error: null,
};

// Async thunk to fetch dashboard collections
export const fetchDashboardCollections = createAsyncThunk(
    'dashboard/fetchDashboardCollections',
    async (warehouseId: number | undefined, { getState, rejectWithValue }) => {
        if (!warehouseId) {
            return rejectWithValue('Warehouse id is required');
        }
        try {
            const state = getState() as RootState;
            const { filter } = state.dashboard;
            let query = `warehouseId=${warehouseId}`;
            if (filter.startDate) {
                query += `&startDate=${filter.startDate}`;
            }
            if (filter.endDate) {
                query += `&endDate=${filter.endDate}`;
            }
            if (filter.status) {
                query += `&status=${filter.status}`;
            }
            const response = await axiosClient.get(`/collections?${query}`);

            return response.data.result; // Adjust depending on API structure
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error fetching dashboard collections');
        }
    }
);

export const fetchDashboardOrders = createAsyncThunk(
    'dashboard/fetchDashboardOrders',
    async (warehouseId: number | undefined, { getState, rejectWithValue }) => {
        if (!warehouseId) {
            console.log('warehouseId', warehouseId);

            return rejectWithValue('Warehouse id is required');
        }
        try {
            const state = getState() as RootState;
            const { filter } = state.dashboard;
            let query = `warehouseId=${warehouseId}`;
            if (filter.startDate) {
                query += `&startDate=${filter.startDate}`;
            }
            if (filter.endDate) {
                query += `&endDate=${filter.endDate}`;
            }
            if (filter.status) {
                query += `&status=${filter.status}`;
            }
            console.log('query', query);

            const response = await axiosClient.get(`/orders?${query}`);

            return response.data.result; // Adjust depending on API structure
        } catch (err: any) {
            return rejectWithValue(err.response?.data || 'Error fetching dashboard orders');
        }
    }
);

// Slice
const dashboardSlice = createSlice({
    name: 'dashboard',
    initialState,
    reducers: {
        setFilter: (state, action: PayloadAction<Partial<DashboardFilter>>) => {
            state.filter = { ...state.filter, ...action.payload };
        },
        clearFilter: (state) => {
            state.filter = initialState.filter;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchDashboardCollections.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchDashboardCollections.fulfilled, (state, action) => {
                state.loading = false;
                state.dashbaordCollects = action.payload;
            })
            .addCase(fetchDashboardCollections.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
            })
            .addCase(fetchDashboardOrders.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchDashboardOrders.fulfilled, (state, action) => {
                state.loading = false;
                state.dashboardOrders = action.payload;
            })
            .addCase(fetchDashboardOrders.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string ?? 'An error occurred';
                state.dashboardOrders = [];
            });
    },
});

export const { setFilter, clearFilter } = dashboardSlice.actions; // Export the action creator

export default dashboardSlice.reducer;
