import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withRepeat,
    withTiming,
} from 'react-native-reanimated';
import useTheme from '../theme/useTheme';

const SCREEN_WIDTH = Dimensions.get('window').width;

export default function CollectionCardSkeleton() {
    const theme = useTheme();
    const opacity = useSharedValue(0.3);

    opacity.value = withRepeat(withTiming(1, { duration: 1000 }), -1, true);

    const animatedStyle = useAnimatedStyle(() => ({
        opacity: opacity.value,
    }));

    return (
        <Animated.View style={[styles.card, { backgroundColor: theme.cardBackground }, animatedStyle]}>
            <View style={styles.row}>
                <View style={[styles.skeletonBox, { width: SCREEN_WIDTH * 0.5, height: 30, backgroundColor: theme.text }]} />
                <View style={[styles.skeletonBox, { width: 60, height: 20, backgroundColor: theme.text }]} />
            </View>
            <View style={styles.row}>
                <View style={[styles.skeletonBox, { width: SCREEN_WIDTH * 0.5, height: 30, backgroundColor: theme.text }]} />
                <View style={[styles.skeletonBox, { width: 60, height: 20, backgroundColor: theme.text }]} />
            </View>

            <View style={[styles.row, { marginTop: 10 }]}>

                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                    <View style={[styles.circle, { backgroundColor: theme.text }]} />
                    <View style={[styles.skeletonBox, { width: 20, height: 16, backgroundColor: theme.text }]} />
                </View>
                <View style={[styles.skeletonBox, { width: 80, height: 16, backgroundColor: theme.text }]} />
                <View style={[styles.skeletonBox, { width: 70, height: 20, borderRadius: 999, backgroundColor: theme.text }]} />
            </View>
        </Animated.View>
    );
}

const styles = StyleSheet.create({
    card: {
        borderRadius: 8,
        padding: 14,
        width: '100%',
        marginVertical: 8,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        marginBottom: 10,
    },
    skeletonBox: {
        borderRadius: 8,
        opacity: 0.3,

    },
    circle: {
        width: 20,
        height: 20,
        borderRadius: 10,
    },
});
