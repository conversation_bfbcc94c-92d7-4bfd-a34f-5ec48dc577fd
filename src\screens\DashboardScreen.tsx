import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Image, TouchableOpacity, ActivityIndicator, ScrollView, RefreshControl } from 'react-native';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import useTheme from '../theme/useTheme';
import GlobalLayout from '../components/GlobalLayout';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../app/store';
import { FontAwesome6 } from '@expo/vector-icons';
import { fetchDashboardCollections, fetchDashboardOrders, setFilter } from '../features/dashboard/dashboardSlice';
import CollectionCard from '../components/CollectionCard';
import { Collection, CollectionStackParamList, DrawerParamList, Order, RootStackParamList } from '../navigation/type';
import RenderEmptyState from '../components/RenderEmptyState';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Animated, { runOnJS, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import OrderCard from '../components/OrderCard';





export default function DashboardScreen() {
    const theme = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const navigation = useNavigation<NativeStackNavigationProp<DrawerParamList>>();
    const { user } = useSelector((state: RootState) => state.auth);
    const { selectedWarehouse } = useSelector((state: RootState) => state.warehouses);
    const { dashbaordCollects, dashboardOrders, loading, error, filter } = useSelector((state: RootState) => state.dashboard);
    const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null)
    const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
    const [Tab, setTab] = useState(3)
    const [currentTab, setCurrentTab] = useState(0)
    const scrollY = useSharedValue(0);
    const stickyHeaderOpacity = useSharedValue(0);
    const stickyHeaderTranslateY = useSharedValue(-60);


    //get collection added today
    useEffect(() => {
        currentTab === 0 ? dispatch(fetchDashboardCollections(selectedWarehouse?.id)) :
            dispatch(fetchDashboardOrders(selectedWarehouse?.id));
    }, [filter, selectedWarehouse?.id])

    const handleTabChange = (tab: number) => {
        setTab(tab);
    };

    const onRefresh = () => {
        currentTab === 0 ? dispatch(fetchDashboardCollections(selectedWarehouse?.id))
            : dispatch(fetchDashboardOrders(selectedWarehouse?.id));
    };


    //set filter end date start date based on the tab
    useEffect(() => {

        if (Tab === 0) {
            dispatch(setFilter({ startDate: new Date().toISOString(), endDate: new Date().toISOString() }));
        }
        if (Tab === 1) {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);
            dispatch(setFilter({ startDate: startDate.toISOString(), endDate: new Date().toISOString() }));
        }
        if (Tab === 2) {
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);
            dispatch(setFilter({ startDate: startDate.toISOString(), endDate: new Date().toISOString() }));
        }
        if (Tab === 3) {
            dispatch(setFilter({ startDate: '', endDate: '' }));
        }
    }, [Tab, dispatch, currentTab])

    const stickyHeaderAnimatedStyle = useAnimatedStyle(() => {
        return {
            opacity: stickyHeaderOpacity.value,
            transform: [{ translateY: stickyHeaderTranslateY.value }],
        };
    });
    const updateStickyHeader = (shouldShow: boolean) => {
        'worklet';
        stickyHeaderOpacity.value = withTiming(shouldShow ? 1 : 0, { duration: 200 });
        stickyHeaderTranslateY.value = withTiming(shouldShow ? 0 : -60, { duration: 200 });
    };
    const handleScroll = (event: any) => {
        const currentScrollY = event.nativeEvent.contentOffset.y;
        scrollY.value = currentScrollY;
        const threshold = 100;
        const shouldShow = currentScrollY >= threshold;

        runOnJS(updateStickyHeader)(shouldShow);
    };


    return (
        <GlobalLayout title="Dashboard">
            {/* overlay */}
            <ThemedView >
                <Animated.View style={[
                    {
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        zIndex: 1000,
                        backgroundColor: theme.background,
                        borderBottomWidth: 1,
                        borderBottomColor: theme.text + '20',
                        paddingHorizontal: 20,
                        paddingVertical: 8,
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: 12,

                    },
                    stickyHeaderAnimatedStyle
                ]}  >
                    <View style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', borderBottomColor: theme.text + '20', borderBottomWidth: 1, }} >
                        <TouchableOpacity onPress={() => setCurrentTab(0)} style={{ flex: 1, borderRadius: 10, padding: 10, borderBottomColor: theme.primary, borderBottomWidth: currentTab === 0 ? 1 : 0 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center', color: currentTab === 0 ? theme.primary : theme.text, fontWeight: 'bold' }}>
                                Collects
                            </ThemedText>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => setCurrentTab(1)} style={{ flex: 1, borderRadius: 10, padding: 10, borderBottomColor: theme.primary, borderBottomWidth: currentTab === 1 ? 1 : 0 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center', color: currentTab === 1 ? theme.primary : theme.text, fontWeight: 'bold' }}>
                                Orders
                            </ThemedText>
                        </TouchableOpacity>

                    </View>


                    <View style={{
                        display: 'flex',
                        backgroundColor: theme.cardBackground,
                        borderRadius: 10,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        paddingHorizontal: 5,
                        paddingVertical: 5,
                        width: '100%',
                    }}>
                        {/* All */}
                        <TouchableOpacity onPress={() => handleTabChange(3)} style={{ position: 'relative', backgroundColor: Tab === 3 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                All
                            </ThemedText>
                            {Tab === 3 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleTabChange(0)} style={{ position: 'relative', backgroundColor: Tab === 0 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                Today
                            </ThemedText>
                            {Tab === 0 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleTabChange(1)} style={{ position: 'relative', backgroundColor: Tab === 1 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                This Week
                            </ThemedText>
                            {Tab === 1 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleTabChange(2)} style={{ position: 'relative', backgroundColor: Tab === 2 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                This Month
                            </ThemedText>
                            {Tab === 2 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                    </View>
                </Animated.View>
                <ScrollView onScroll={handleScroll} refreshControl={
                    <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor={theme.primary} />
                } style={{ flex: 1, paddingHorizontal: 15 }}>
                    <View style={styles.header}>
                        <View style={styles.logo}>
                            {user.logo ? <Image source={{ uri: user.logo }} style={{ width: 50, height: 50, borderRadius: 5 }} />
                                : <FontAwesome6 name="user" size={15} color={theme.text} />}
                        </View>
                        <View style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                            <ThemedText variant="body" style={{}}>
                                Hello, 👋 Good Morning
                            </ThemedText>
                            <ThemedText variant="title" style={{}}>
                                {user.name}
                            </ThemedText>
                        </View>
                    </View>
                    <View style={{ width: '100%', height: 1, backgroundColor: theme.text + '20', }} />

                    <View style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', borderBottomColor: theme.text + '20', borderBottomWidth: 1, marginBottom: 10 }} >
                        <TouchableOpacity onPress={() => setCurrentTab(0)} style={{ flex: 1, borderRadius: 10, padding: 10, borderBottomColor: theme.primary, borderBottomWidth: currentTab === 0 ? 1 : 0 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center', color: currentTab === 0 ? theme.primary : theme.text, fontWeight: 'bold' }}>
                                Collects
                            </ThemedText>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => setCurrentTab(1)} style={{ flex: 1, borderRadius: 10, padding: 10, borderBottomColor: theme.primary, borderBottomWidth: currentTab === 1 ? 1 : 0 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center', color: currentTab === 1 ? theme.primary : theme.text, fontWeight: 'bold' }}>
                                Orders
                            </ThemedText>
                        </TouchableOpacity>

                    </View>


                    <View style={{
                        display: 'flex',
                        backgroundColor: theme.cardBackground,
                        borderRadius: 10,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        paddingHorizontal: 5,
                        paddingVertical: 5,
                    }}>
                        {/* All */}
                        <TouchableOpacity onPress={() => handleTabChange(3)} style={{ backgroundColor: Tab === 3 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                All
                            </ThemedText>
                            {Tab === 3 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleTabChange(0)}
                            style={{ position: 'relative', backgroundColor: Tab === 0 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                Today
                            </ThemedText>
                            {Tab === 0 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleTabChange(1)} style={{ position: 'relative', backgroundColor: Tab === 1 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                This Week
                            </ThemedText>
                            {Tab === 1 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => handleTabChange(2)} style={{ position: 'relative', backgroundColor: Tab === 2 ? theme.background : theme.cardBackground, borderRadius: 10, padding: 10 }}>
                            <ThemedText variant="body" style={{ textAlign: 'center' }}>
                                This Month
                            </ThemedText>
                            {Tab === 2 && <View
                                style={{
                                    position: 'absolute',
                                    width: 30, bottom: 0, backgroundColor: theme.primary,
                                    borderRadius: 20, height: 5, left: '50%',
                                    transform: [{ translateX: -5 }],
                                }} />}
                        </TouchableOpacity>
                    </View>
                    {currentTab === 0 && (
                        <View style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 10, justifyContent: 'center', alignItems: 'center', marginTop: 10 }} >
                            {!loading && dashbaordCollects.length === 0 ? <RenderEmptyState state="Collection" subtitle={`No collections added ${Tab === 0 ? 'today' : Tab === 1 ? 'this week' : 'this month'}`} /> :
                                dashbaordCollects.map((collection, index) => (
                                    <CollectionCard
                                        key={collection.id + '-' + index}
                                        selected={selectedCollection?.id === collection.id}
                                        onOptionClick={() => {
                                            selectedCollection?.id === collection.id
                                                ? setSelectedCollection(null)
                                                : setSelectedCollection(collection)
                                        }}
                                        collection={collection}
                                        onPress={() => {
                                            console.log('collection fefe', collection.id);
                                            navigation.navigate('CollectionDetails', { collectionId: collection.id })
                                        }}
                                    />

                                ))}
                        </View>)}
                    {
                        currentTab === 1 && (
                            <View style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 10, justifyContent: 'center', alignItems: 'center', marginTop: 10 }} >
                                {!loading && dashboardOrders.length === 0 ? <RenderEmptyState state="Order" subtitle={`No collections added ${Tab === 0 ? 'today' : Tab === 1 ? 'this week' : 'this month'}`} /> :
                                    dashboardOrders.map((order, index) => (
                                        <OrderCard
                                            key={order.id + '-' + index}
                                            selected={selectedOrder?.id === order.id}
                                            onOptionClick={() => {
                                                selectedCollection?.id === order.id
                                                    ? setSelectedOrder(null)
                                                    : setSelectedOrder(order)
                                            }}
                                            order={order}
                                            onPress={() => {
                                                console.log('order fefe', order.id);
                                                // navigation.navigate('CollectionDetails', { collectionId: order.id })
                                            }}
                                        />

                                    ))}
                            </View>
                        )
                    }




                </ScrollView>



            </ThemedView>
        </GlobalLayout>

    );
}

const styles = StyleSheet.create({

    header: {
        width: '100%',
        gap: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 16,
    },
    logo: {
        width: 50,
        height: 50,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },


});
