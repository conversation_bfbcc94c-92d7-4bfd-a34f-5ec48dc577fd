import { AntDesign, FontAwesome5 } from "@expo/vector-icons";
import { StyleSheet, View } from "react-native";
import ThemedText from "./ThemedText";
import useTheme from "../theme/useTheme";

export default function RenderEmptyState({ state, subtitle }: { state: string, subtitle?: string }) {
    const theme = useTheme();

    const renderIcon = () => {
        switch (state) {
            case 'Return':
                return <AntDesign name="inbox" size={64} color={theme.text + '40'} />;
            case 'Collection':
                return <FontAwesome5 name="box" size={64} color={theme.text + '40'} />;
            case 'Order':
                return <AntDesign name="inbox" size={64} color={theme.text + '40'} />;
            case 'Warehouse':
                return <FontAwesome5 name="warehouse" size={24} color={theme.text + '40'} />;
            case 'Recipient':
                return <FontAwesome5 name="user" size={64} color={theme.text + '40'} />;
            default:
                return <AntDesign name="inbox" size={64} color={theme.text + '40'} />;
        }
    };

    return (
        <View style={styles.emptyState}>
            {renderIcon()}
            <ThemedText variant="title" style={[styles.emptyTitle, { color: theme.text + '60' }]}>
                No {state}s
            </ThemedText>
            <ThemedText variant="body" style={[styles.emptyDescription, { color: theme.text + '40' }]}>
                {subtitle ? subtitle : `No ${state}s have been created yet.`}
            </ThemedText>
        </View>

    );
}

const styles = StyleSheet.create({

    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 40,
        marginTop: 40,
    },
    emptyTitle: {
        marginTop: 16,
        marginBottom: 8,
    },
    emptyDescription: {
        textAlign: 'center',
        lineHeight: 20,
    },
});
