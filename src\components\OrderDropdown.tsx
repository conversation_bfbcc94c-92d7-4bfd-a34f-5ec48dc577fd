import React, { useEffect, useMemo, useState } from 'react';
import { View, TouchableOpacity, FlatList, StyleSheet, Modal, Pressable } from 'react-native';
import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';
import ThemedText from './ThemedText';
import useTheme from '../theme/useTheme';
import { Order } from '../navigation/type';
import dayjs from 'dayjs';
import RenderEmptyState from './RenderEmptyState';
import { useDispatch, useSelector } from 'react-redux';
import { closeModal, openModal, selectIsModalOpen } from '../features/modal/modalsSlice';

type Props = {
    orders: Order[];

    /** Single or "1;2;3" when you pass joined ids */
    selectedOrderId?: number | string;

    /** Preferred explicit multiple selected ids */
    selectedOrderIds?: number[];

    /** Single: (order) => void, Multiple: (orders[]) => void */
    onSelect: (selection: Order | Order[]) => void;

    placeholder?: string;
    disabled?: boolean;
    onRefresh?: () => void;
    loading?: boolean;

    /** Enable multiple select */
    isMultiple?: boolean;

    /** Give each dropdown its own id to avoid clashes */
    modalId?: string;
};

export default function OrderDropdown({
    orders,
    selectedOrderId,
    selectedOrderIds,
    onSelect,
    placeholder = 'Select an order',
    disabled = false,
    onRefresh = () => { },
    loading = false,
    isMultiple = false,
    modalId = 'order-dropdown',
}: Props) {
    const theme = useTheme();
    const dispatch = useDispatch();
    const isOpen = useSelector(selectIsModalOpen(modalId));
    const open = () => dispatch(openModal({ id: modalId }));
    const close = () => dispatch(closeModal(modalId));

    // ---- derive selected ids (supports number | "1;2;3" | number[]) ----
    const selectedIds = useMemo<number[]>(() => {
        if (Array.isArray(selectedOrderIds)) return selectedOrderIds;
        if (typeof selectedOrderId === 'string' && selectedOrderId.includes(';')) {
            return selectedOrderId
                .split(';')
                .map(s => s.trim())
                .filter(Boolean)
                .map(n => Number(n))
                .filter(n => !Number.isNaN(n));
        }
        if (typeof selectedOrderId === 'number') return [selectedOrderId];
        return [];
    }, [selectedOrderId, selectedOrderIds]);

    const selectedOrder = useMemo(
        () => orders.find(o => o.id === (typeof selectedOrderId === 'number' ? selectedOrderId : Number(selectedOrderId))) || null,
        [orders, selectedOrderId]
    );

    // ---- local selection buffer for the modal (only used in multi mode) ----
    const [buffer, setBuffer] = useState<Set<number>>(new Set(selectedIds));

    useEffect(() => {
        if (isOpen) {
            // refresh buffer when modal opens so it mirrors current props
            setBuffer(new Set(selectedIds));
        }
    }, [isOpen, selectedIds]);

    const toggleBuffer = (id: number) => {
        setBuffer(prev => {
            const next = new Set(prev);
            if (next.has(id)) next.delete(id);
            else next.add(id);
            return next;
        });
    };

    const applySelection = () => {
        if (!isMultiple) return;
        const pickedIds = Array.from(buffer);
        const pickedOrders = orders.filter(o => pickedIds.includes(o.id));
        onSelect(pickedOrders);
        close();
    };

    const handleSinglePick = (order: Order) => {
        if (isMultiple) return; // should not hit
        onSelect(order);
        close();
    };

    const renderOrderItem = ({ item }: { item: Order }) => {
        const isSelected = isMultiple ? buffer.has(item.id) : selectedIds.includes(item.id);

        return (
            <TouchableOpacity
                style={[
                    styles.dropdownItem,
                    {
                        borderBottomColor: theme.text + '20',
                        backgroundColor: isSelected ? theme.primary + '10' : 'transparent',
                    },
                ]}
                onPress={() => {
                    if (isMultiple) toggleBuffer(item.id);
                    else handleSinglePick(item);
                }}
            >
                <View style={{ flex: 1, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                    <ThemedText
                        variant="subtitle"
                        style={[styles.orderNum, isSelected && { color: theme.primary }]}
                    >
                        Order {item.orderNum}
                    </ThemedText>
                    <ThemedText
                        variant="body"
                        style={[styles.orderDescription, { color: theme.text + '70' }]}
                        numberOfLines={1}
                    >
                        Shipped : {dayjs(item.shippedAt).fromNow()}
                    </ThemedText>
                </View>

                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                        <MaterialCommunityIcons
                            name="package-variant-closed"
                            size={24}
                            color={isSelected ? theme.primary : 'black'}
                        />
                        <ThemedText
                            variant="body"
                            numberOfLines={1}
                            style={isSelected && { color: theme.primary }}
                        >
                            {item.ordersProducts.length}
                        </ThemedText>
                    </View>

                    {/* {isMultiple ? (
                        <MaterialCommunityIcons
                            name={isSelected ? 'checkbox-marked' : 'checkbox-blank-outline'}
                            size={22}
                            color={isSelected ? theme.primary : theme.text + '60'}
                        />
                    ) : (
                        <AntDesign name="right" size={14} color={theme.text + '60'} />
                    )} */}
                </View>
            </TouchableOpacity>
        );
    };

    // ---- field preview (collapsed) ----
    const multiCount = selectedIds.length;
    const firstSelected = useMemo(
        () => (multiCount ? orders.find(o => o.id === selectedIds[0]) || null : null),
        [orders, multiCount, selectedIds]
    );

    return (
        <>
            <TouchableOpacity
                style={[
                    styles.dropdown,
                    {
                        backgroundColor: theme.inputBackground,
                        borderColor: theme.text + '30',
                        opacity: disabled ? 0.5 : 1,
                    },
                ]}
                onPress={() => !disabled && open()}
                disabled={disabled}
            >
                <View style={styles.dropdownContent}>
                    {(!isMultiple && selectedOrder) ? (
                        // Single preview
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', gap: 12 }}>
                            <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                                <MaterialCommunityIcons
                                    name="package-variant-closed"
                                    size={24}
                                    color={theme.primary}
                                />
                                <ThemedText variant="body" numberOfLines={1} style={{ color: theme.primary }}>
                                    {selectedOrder.ordersProducts.length}
                                </ThemedText>
                            </View>
                            <View style={{ flex: 1, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
                                <ThemedText variant="subtitle" style={[styles.orderNum, { color: theme.primary }]}>
                                    Order {selectedOrder.orderNum}
                                </ThemedText>
                                <ThemedText variant="body" style={[styles.orderDescription, { color: theme.text + '70' }]} numberOfLines={1}>
                                    Shipped : {dayjs(selectedOrder.shippedAt || '2025-07-11').fromNow()}
                                </ThemedText>
                            </View>
                        </View>
                    ) : isMultiple && multiCount > 0 ? (
                        // Multiple preview
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                            <MaterialCommunityIcons name="checkbox-multiple-marked" size={20} color={theme.primary} />
                            <ThemedText variant="subtitle" style={{ color: theme.primary }}>
                                {multiCount} selected
                            </ThemedText>
                            {firstSelected && (
                                <ThemedText variant="body" style={{ color: theme.text + '70' }} numberOfLines={1}>
                                    • Order {firstSelected.orderNum}
                                </ThemedText>
                            )}
                        </View>
                    ) : (
                        <ThemedText variant="body" style={[styles.placeholder, { color: theme.text + '60' }]}>
                            {placeholder}
                        </ThemedText>
                    )}
                </View>

                <AntDesign name={isOpen ? 'up' : 'down'} size={16} color={theme.text + '60'} />
            </TouchableOpacity>

            <Modal visible={isOpen} transparent animationType="fade" onRequestClose={close}>
                {/* No dark bg here; your GlobalOverlay handles the backdrop */}
                <Pressable style={{ justifyContent: 'center', paddingHorizontal: 20, flex: 1, marginTop: 24 }} onPress={close}>
                    <View style={[styles.dropdownModal, { backgroundColor: theme.cardBackground }]}>
                        {/* Header */}
                        <View style={styles.modalHeader}>
                            <ThemedText variant="title">{isMultiple ? 'Select Orders' : 'Select Order'}</ThemedText>

                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                                {isMultiple && (
                                    <>
                                        {/* Select all / clear buttons are optional */}
                                        <TouchableOpacity onPress={() => setBuffer(new Set(orders.map(o => o.id)))}>
                                            <ThemedText variant="body" style={{ color: theme.primary }}>Select all</ThemedText>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => setBuffer(new Set())}>
                                            <ThemedText variant="body" style={{ color: theme.text + '80' }}>Clear</ThemedText>
                                        </TouchableOpacity>
                                    </>
                                )}
                                <TouchableOpacity onPress={close}>
                                    <AntDesign name="close" size={24} color={theme.text} />
                                </TouchableOpacity>
                            </View>
                        </View>

                        <FlatList
                            contentContainerStyle={[orders.length === 0 && { flex: 1, padding: 20 }]}
                            data={orders}
                            renderItem={renderOrderItem}
                            keyExtractor={item => item.id.toString()}
                            style={styles.ordersList}
                            showsVerticalScrollIndicator={false}
                            refreshing={loading}
                            onRefresh={onRefresh}
                            ListEmptyComponent={() => <RenderEmptyState state="order" />}
                        />

                        {isMultiple && (
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 12, borderTopWidth: 1, borderTopColor: theme.text + '20' }}>
                                <ThemedText variant="body" style={{ color: theme.text + '70' }}>
                                    {buffer.size} selected
                                </ThemedText>
                                <View style={{ flexDirection: 'row', gap: 8 }}>
                                    <TouchableOpacity onPress={close} style={{ paddingVertical: 10, paddingHorizontal: 14, borderRadius: 8, borderWidth: 1, borderColor: theme.text + '30' }}>
                                        <ThemedText variant="body">Cancel</ThemedText>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={applySelection} style={{ paddingVertical: 10, paddingHorizontal: 14, borderRadius: 8, backgroundColor: theme.primary }}>
                                        <ThemedText variant="body" style={{ color: 'white' }}>Apply</ThemedText>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        )}
                    </View>
                </Pressable>
            </Modal>
        </>
    );
}

const styles = StyleSheet.create({
    dropdown: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
        borderWidth: 1,
        minHeight: 56,
    },
    dropdownContent: {
        flex: 1,
        marginRight: 8,
    },
    placeholder: {
        fontSize: 16,
    },
    dropdownModal: {
        borderRadius: 12,
        maxHeight: '70%',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    ordersList: {
        maxHeight: 300,
    },
    dropdownItem: {
        padding: 16,
        borderBottomWidth: 1,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    orderNum: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    orderDescription: {
        fontSize: 14,
        lineHeight: 18,
    },
});
