
import React from 'react';
import { GestureResponderEvent, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import useTheme from '../theme/useTheme';
import ThemedText from './ThemedText';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import StatusRenderer from './StatusRenderer';
import { Entypo, Feather, FontAwesome, FontAwesome6, MaterialCommunityIcons, MaterialIcons, SimpleLineIcons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';
dayjs.extend(relativeTime);

type Props = {
    collection: any;
    onPress: (event: GestureResponderEvent) => void;
    onOptionClick: (collection: any) => void;
    selected: boolean;
};

export default function CollectionCard({ collection, onPress, onOptionClick, selected }: Props) {
    const theme = useTheme();

    return (
        <View style={{ width: '100%' }} >

            <View
                // onPress={onPress}
                style={[styles.button, { backgroundColor: selected ? theme.primary + '20' : theme.cardBackground }]}
            >
                <View style={{ flex: 1, width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >
                    <ThemedText variant='title'>No.{collection.num}</ThemedText>
                    {/* three point settings  */}
                    <TouchableOpacity onPress={() => onOptionClick(collection)} style={{ height: 34, width: 34, display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center', borderRadius: 10 }}>
                        {/* {selected ? <Feather name="check-square" size={24} color={theme.text} /> : <Feather name="square" size={24} color={theme.text} />} */}
                        <SimpleLineIcons name="options" size={24} color={theme.text} />
                    </TouchableOpacity>
                </View>

                <View style={{ flex: 1, width: '100%', marginTop: 10, display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }} >
                    <ThemedText variant='body'>{dayjs(collection.addedAt).format('DD/MM/YYYY')}</ThemedText>
                    <View style={{ display: 'flex', flexDirection: 'row', gap: 4, justifyContent: 'center', alignItems: 'center' }}>
                        <FontAwesome name="cube" size={20} color={theme.text} />
                        <ThemedText variant='body'>{collection.orders}</ThemedText>
                    </View>
                    <StatusRenderer status={collection.status} />

                </View>
                <View
                    style={{
                        flex: 1,
                        width: '100%',
                        marginTop: 20,
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        borderTopWidth: 1,
                        borderTopColor: theme.text + '20',
                    }}
                >
                    <TouchableOpacity
                        onPress={onPress}
                        style={{
                            flexShrink: 1, // prevents stretching
                            flexGrow: 0,   // don't grow to fill space
                            flexDirection: 'row',
                            alignItems: 'center',
                            padding: 5,
                            height: 40,
                        }}
                    >
                        <ThemedText variant="body">See Details</ThemedText>
                        <Entypo name="chevron-right" size={24} color={theme.text} />
                    </TouchableOpacity>
                </View>


            </View>




        </View>
    );
}

const styles = StyleSheet.create({
    button: {
        borderRadius: 8,
        paddingTop: 14,
        paddingRight: 14,
        paddingLeft: 14,
        alignItems: 'center',
    },
    text: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    option: {
        height: 40,
        display: 'flex',
        flexDirection: 'row',
        gap: 10,
        alignItems: 'center',
    },
});
